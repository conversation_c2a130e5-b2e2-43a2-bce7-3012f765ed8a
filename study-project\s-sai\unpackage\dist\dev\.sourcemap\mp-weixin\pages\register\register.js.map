{"version": 3, "file": "register.js", "sources": ["pages/register/register.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVnaXN0ZXIvcmVnaXN0ZXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <view class=\"header\">\r\n      <view class=\"logo\">👤</view>\r\n      <view class=\"title\">人脸注册</view>\r\n      <view class=\"subtitle\">请上传清晰的人脸照片进行注册</view>\r\n    </view>\r\n    \r\n    <view class=\"card\">\r\n      <view class=\"upload-section\">\r\n        <view class=\"upload-area\" @tap=\"chooseImage\">\r\n          <view v-if=\"!registerImage\" class=\"upload-placeholder\">\r\n            <view class=\"upload-icon\">📷</view>\r\n            <text class=\"upload-text\">点击上传人脸图片</text>\r\n            <text class=\"upload-hint\">支持拍照或从相册选择</text>\r\n          </view>\r\n          <image v-else :src=\"registerImage\" class=\"uploaded-image\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"form-section\">\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">姓名</text>\r\n          <input \r\n            v-model=\"registerName\" \r\n            placeholder=\"请输入您的姓名\" \r\n            class=\"input\"\r\n            maxlength=\"20\"\r\n          />\r\n        </view>\r\n        \r\n        <view class=\"form-item\">\r\n          <text class=\"label\">备注</text>\r\n          <input \r\n            v-model=\"registerRemark\" \r\n            placeholder=\"请输入备注信息（可选）\" \r\n            class=\"input\"\r\n            maxlength=\"50\"\r\n          />\r\n        </view>\r\n      </view>\r\n      \r\n      <button \r\n        class=\"submit-btn\" \r\n        @tap=\"submitRegister\" \r\n        :loading=\"registerLoading\"\r\n        :disabled=\"!canSubmit\"\r\n      >\r\n        <text v-if=\"!registerLoading\" class=\"btn-text\">{{ registerLoading ? '注册中...' : '立即注册' }}</text>\r\n        <view v-else class=\"loading-dots\">\r\n          <view class=\"dot\"></view>\r\n          <view class=\"dot\"></view>\r\n          <view class=\"dot\"></view>\r\n        </view>\r\n      </button>\r\n      \r\n      <view v-if=\"registerResult\" class=\"result-message\">\r\n        {{ registerResult }}\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"footer\">\r\n      <text class=\"footer-text\">已有账号？</text>\r\n      <text class=\"login-link\" @tap=\"goToLogin\">立即登录</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      registerImage: '',\r\n      registerName: '',\r\n      registerRemark: '',\r\n      registerLoading: false,\r\n      registerResult: ''\r\n    }\r\n  },\r\n  computed: {\r\n    canSubmit() {\r\n      return this.registerImage && this.registerName.trim();\r\n    }\r\n  },\r\n  methods: {\r\n    chooseImage() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album', 'camera'],\r\n        success: res => {\r\n          this.registerImage = res.tempFilePaths[0];\r\n          this.registerResult = '';\r\n        }\r\n      });\r\n    },\r\n    \r\n    submitRegister() {\r\n      if (!this.canSubmit) {\r\n        this.registerResult = '请上传人脸图片并填写姓名';\r\n        return;\r\n      }\r\n      \r\n      this.registerLoading = true;\r\n      this.registerResult = '';\r\n      \r\n      uni.uploadFile({\r\n        url: 'http://localhost:8081/fact-info/save',\r\n        filePath: this.registerImage,\r\n        name: 'files',\r\n        formData: {\r\n          name: this.registerName.trim(),\r\n          remark: this.registerRemark.trim()\r\n        },\r\n        success: res => {\r\n          let data = {};\r\n          try {\r\n            data = JSON.parse(res.data);\r\n          } catch (e) {\r\n            this.registerResult = '注册失败，返回数据异常';\r\n            return;\r\n          }\r\n          \r\n          if (data.code === 200) {\r\n            this.registerResult = '✅ 注册成功！欢迎 ' + data.data.name;\r\n            setTimeout(() => {\r\n              this.registerImage = '';\r\n              this.registerName = '';\r\n              this.registerRemark = '';\r\n            }, 2000);\r\n          } else {\r\n            this.registerResult = '❌ 注册失败：' + data.message;\r\n          }\r\n        },\r\n        fail: err => {\r\n          this.registerResult = '❌ 注册失败：' + err.errMsg;\r\n        },\r\n        complete: () => {\r\n          this.registerLoading = false;\r\n        }\r\n      });\r\n    },\r\n    \r\n    goToLogin() {\r\n      uni.navigateTo({\r\n        url: '/pages/login/login'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);\r\n  padding: 40rpx 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\r\n  opacity: 0.3;\r\n  pointer-events: none;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 60rpx;\r\n  padding-top: 60rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.logo {\r\n  font-size: 100rpx;\r\n  margin-bottom: 30rpx;\r\n  animation: float 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% { transform: translateY(0px); }\r\n  50% { transform: translateY(-10px); }\r\n}\r\n\r\n.title {\r\n  font-size: 48rpx;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  margin-bottom: 20rpx;\r\n  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);\r\n  letter-spacing: 2rpx;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 28rpx;\r\n  color: rgba(255,255,255,0.9);\r\n  line-height: 1.5;\r\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.card {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border-radius: 24rpx;\r\n  padding: 40rpx 32rpx;\r\n  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\r\n  backdrop-filter: blur(20rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.upload-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.upload-area {\r\n  border: 3rpx dashed #ddd;\r\n  border-radius: 20rpx;\r\n  padding: 60rpx 20rpx;\r\n  text-align: center;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #f0f2ff 100%);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.upload-area::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.upload-area:active::before {\r\n  left: 100%;\r\n}\r\n\r\n.upload-area:active {\r\n  border-color: #667eea;\r\n  background: linear-gradient(135deg, #f0f2ff 0%, #e8ecff 100%);\r\n  transform: scale(0.98);\r\n}\r\n\r\n.upload-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 80rpx;\r\n  margin-bottom: 20rpx;\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n.upload-text {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.uploaded-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  border-radius: 20rpx;\r\n  border: 4rpx solid #e1e5e9;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n  animation: fadeIn 0.5s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: scale(0.8); }\r\n  to { opacity: 1; transform: scale(1); }\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 32rpx;\r\n}\r\n\r\n.label {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.input {\r\n  width: 100%;\r\n  border: 2rpx solid #e1e5e9;\r\n  border-radius: 16rpx;\r\n  padding: 24rpx 20rpx;\r\n  font-size: 28rpx;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #f0f2ff 100%);\r\n  transition: all 0.3s ease;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.input:focus {\r\n  border-color: #667eea;\r\n  background: #fff;\r\n  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\r\n  transform: translateY(-2rpx);\r\n}\r\n\r\n.submit-btn {\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  border-radius: 20rpx;\r\n  padding: 28rpx 0;\r\n  border: none;\r\n  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.submit-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.submit-btn:active::before {\r\n  left: 100%;\r\n}\r\n\r\n.submit-btn:active {\r\n  transform: translateY(4rpx);\r\n  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.submit-btn[disabled] {\r\n  background: #ccc;\r\n  box-shadow: none;\r\n  transform: none;\r\n}\r\n\r\n.btn-text {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.loading-dots {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.dot {\r\n  width: 12rpx;\r\n  height: 12rpx;\r\n  background: #fff;\r\n  border-radius: 50%;\r\n  animation: loading 1.4s ease-in-out infinite both;\r\n}\r\n\r\n.dot:nth-child(1) { animation-delay: -0.32s; }\r\n.dot:nth-child(2) { animation-delay: -0.16s; }\r\n\r\n@keyframes loading {\r\n  0%, 80%, 100% { transform: scale(0); }\r\n  40% { transform: scale(1); }\r\n}\r\n\r\n.result-message {\r\n  margin-top: 24rpx;\r\n  padding: 24rpx 32rpx;\r\n  border-radius: 16rpx;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  text-align: center;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #fff;\r\n  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.2);\r\n  animation: slideIn 0.5s ease;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from { opacity: 0; transform: translateY(20rpx); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  margin-top: auto;\r\n  padding-bottom: 40rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.footer-text {\r\n  font-size: 28rpx;\r\n  color: rgba(255,255,255,0.8);\r\n}\r\n\r\n.login-link {\r\n  font-size: 28rpx;\r\n  color: #fff;\r\n  font-weight: 600;\r\n  margin-left: 12rpx;\r\n  text-decoration: underline;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-link:active {\r\n  transform: scale(0.95);\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/study/study-project/s-sai/pages/register/register.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqEA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IAClB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,YAAY;AACV,aAAO,KAAK,iBAAiB,KAAK,aAAa,KAAI;AAAA,IACrD;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,SAAO;AACd,eAAK,gBAAgB,IAAI,cAAc,CAAC;AACxC,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACf,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,iBAAiB;AACtB;AAAA,MACF;AAEA,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AAEtBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,UAAU,KAAK;AAAA,QACf,MAAM;AAAA,QACN,UAAU;AAAA,UACR,MAAM,KAAK,aAAa,KAAM;AAAA,UAC9B,QAAQ,KAAK,eAAe,KAAK;AAAA,QAClC;AAAA,QACD,SAAS,SAAO;AACd,cAAI,OAAO,CAAA;AACX,cAAI;AACF,mBAAO,KAAK,MAAM,IAAI,IAAI;AAAA,UAC5B,SAAS,GAAG;AACV,iBAAK,iBAAiB;AACtB;AAAA,UACF;AAEA,cAAI,KAAK,SAAS,KAAK;AACrB,iBAAK,iBAAiB,eAAe,KAAK,KAAK;AAC/C,uBAAW,MAAM;AACf,mBAAK,gBAAgB;AACrB,mBAAK,eAAe;AACpB,mBAAK,iBAAiB;AAAA,YACvB,GAAE,GAAI;AAAA,iBACF;AACL,iBAAK,iBAAiB,YAAY,KAAK;AAAA,UACzC;AAAA,QACD;AAAA,QACD,MAAM,SAAO;AACX,eAAK,iBAAiB,YAAY,IAAI;AAAA,QACvC;AAAA,QACD,UAAU,MAAM;AACd,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,YAAY;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpJA,GAAG,WAAW,eAAe;"}