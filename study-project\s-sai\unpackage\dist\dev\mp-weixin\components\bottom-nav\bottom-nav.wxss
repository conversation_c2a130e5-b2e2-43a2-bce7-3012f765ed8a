
.bottom-nav.data-v-55d8123a {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}
.nav-item.data-v-55d8123a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}
.nav-item.active.data-v-55d8123a {
  color: #007AFF;
}
.nav-icon.data-v-55d8123a {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}
.nav-text.data-v-55d8123a {
  font-size: 20rpx;
  color: #666;
}
.nav-item.active .nav-text.data-v-55d8123a {
  color: #007AFF;
  font-weight: 500;
}
