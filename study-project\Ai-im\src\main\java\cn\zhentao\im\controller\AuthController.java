package cn.zhentao.im.controller;

import cn.zhentao.im.dto.ApiResponse;
import cn.zhentao.im.dto.LoginRequest;
import cn.zhentao.im.dto.RegisterRequest;
import cn.zhentao.im.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 认证控制器
 * 处理用户登录、注册、退出等请求
 */
@Slf4j
@RestController
@RequestMapping("/api/im/auth")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AuthController {

    private final UserService userService;

    /**
     * 用户注册接口
     * POST /api/im/auth/register
     */
    @PostMapping("/register")
    public ApiResponse<Map<String, Object>> register(@Validated @RequestBody RegisterRequest request) {
        try {
            Map<String, Object> result = userService.register(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 用户登录接口
     * POST /api/im/auth/login
     */
    @PostMapping("/login")
    public ApiResponse<Map<String, Object>> login(@Validated @RequestBody LoginRequest request) {
        try {
            Map<String, Object> result = userService.login(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 用户退出登录接口
     * POST /api/im/auth/logout
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout(@RequestHeader("Authorization") String token) {
        try {
            // 这里可以实现token失效逻辑
            log.info("用户退出登录");
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("用户退出失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }
}
