"use strict";
const common_vendor = require("../../common/vendor.js");
const AiHome = () => "../ai-home/ai-home2.js";
const BottomNav = () => "../../components/bottom-nav/bottom-nav.js";
const _sfc_main = {
  name: "Main",
  components: {
    AiHome,
    BottomNav
  },
  data() {
    return {
      currentTab: "home"
      // 默认显示主页（AI智能家教）
    };
  },
  methods: {
    handleTabChange(tab) {
      this.currentTab = tab;
    }
  }
};
if (!Array) {
  const _component_ai_home = common_vendor.resolveComponent("ai-home");
  const _easycom_bottom_nav2 = common_vendor.resolveComponent("bottom-nav");
  (_component_ai_home + _easycom_bottom_nav2)();
}
const _easycom_bottom_nav = () => "../../components/bottom-nav/bottom-nav.js";
if (!Math) {
  _easycom_bottom_nav();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentTab === "home"
  }, $data.currentTab === "home" ? {} : $data.currentTab === "learn" ? {} : $data.currentTab === "course" ? {} : $data.currentTab === "question" ? {} : $data.currentTab === "premium" ? {} : $data.currentTab === "ai-home" ? {} : {}, {
    b: $data.currentTab === "learn",
    c: $data.currentTab === "course",
    d: $data.currentTab === "question",
    e: $data.currentTab === "premium",
    f: $data.currentTab === "ai-home",
    g: common_vendor.o($options.handleTabChange),
    h: common_vendor.p({
      activeTab: $data.currentTab
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4f50ca8f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/main/main.js.map
