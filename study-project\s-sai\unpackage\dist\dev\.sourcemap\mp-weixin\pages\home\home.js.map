{"version": 3, "file": "home.js", "sources": ["pages/home/<USER>", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaG9tZS9ob21lLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 自定义状态栏 -->\n    <view class=\"custom-status-bar\">\n      <view class=\"status-content\">\n        <text class=\"app-title\">首页</text>\n      </view>\n    </view>\n\n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- 轮播图 -->\n      <view class=\"banner-section\">\n        <swiper class=\"banner-swiper\" indicator-dots=\"true\" autoplay=\"true\" interval=\"3000\" duration=\"500\">\n          <swiper-item v-for=\"(banner, index) in bannerList\" :key=\"index\">\n            <view class=\"banner-item\" :style=\"'background: ' + banner.bgColor\">\n              <view class=\"banner-content\">\n                <text class=\"banner-title\">{{ banner.title }}</text>\n                <text class=\"banner-desc\">{{ banner.desc }}</text>\n                <view class=\"banner-icon\">{{ banner.icon }}</view>\n              </view>\n            </view>\n          </swiper-item>\n        </swiper>\n      </view>\n\n      <!-- AI语音对话区域 -->\n      <view class=\"ai-chat-section\">\n        <view class=\"section-title\">\n          <text class=\"title-icon\">🤖</text>\n          <text class=\"title-text\">AI智能对话</text>\n        </view>\n        <view class=\"chat-card\" @tap=\"startVoiceChat\">\n          <view class=\"chat-content\">\n            <view class=\"voice-icon\">🎤</view>\n            <text class=\"chat-title\">开启AI模式与AI对话</text>\n            <text class=\"chat-desc\">语音交互，智能回答各种问题</text>\n          </view>\n          <view class=\"chat-arrow\">→</view>\n        </view>\n      </view>\n\n      <!-- 功能模块网格 -->\n      <view class=\"functions-section\">\n        <view class=\"section-title\">\n          <text class=\"title-icon\">⭐</text>\n          <text class=\"title-text\">智能功能</text>\n        </view>\n        <view class=\"functions-grid\">\n          <!-- 第一行 -->\n          <view class=\"function-item\" @tap=\"openFunction('knowledge')\" @longpress=\"showFunctionDetail('knowledge')\">\n            <view class=\"function-icon knowledge\">📚</view>\n            <text class=\"function-name\">知识问答</text>\n            <text class=\"function-desc\">AI百科全书</text>\n          </view>\n          <view class=\"function-item\" @tap=\"openFunction('search')\" @longpress=\"showFunctionDetail('search')\">\n            <view class=\"function-icon search\">🔍</view>\n            <text class=\"function-name\">信息查询</text>\n            <text class=\"function-desc\">天气·电话·资讯</text>\n          </view>\n          <view class=\"function-item\" @tap=\"openFunction('writing')\" @longpress=\"showFunctionDetail('writing')\">\n            <view class=\"function-icon writing\">✍️</view>\n            <text class=\"function-name\">文本生成</text>\n            <text class=\"function-desc\">作文·故事·诗歌</text>\n          </view>\n\n          <!-- 第二行 -->\n          <view class=\"function-item\" @tap=\"openFunction('translate')\" @longpress=\"showFunctionDetail('translate')\">\n            <view class=\"function-icon translate\">🌐</view>\n            <text class=\"function-name\">语言翻译</text>\n            <text class=\"function-desc\">多语言互译</text>\n          </view>\n          <view class=\"function-item\" @tap=\"openFunction('emotion')\" @longpress=\"showFunctionDetail('emotion')\">\n            <view class=\"function-icon emotion\">💝</view>\n            <text class=\"function-name\">情感陪伴</text>\n            <text class=\"function-desc\">情感识别回应</text>\n          </view>\n          <view class=\"function-item\" @tap=\"openFunction('recommend')\" @longpress=\"showFunctionDetail('recommend')\">\n            <view class=\"function-icon recommend\">🎯</view>\n            <text class=\"function-name\">智能推荐</text>\n            <text class=\"function-desc\">个性化内容</text>\n          </view>\n\n          <!-- 第三行 -->\n          <view class=\"function-item\" @tap=\"openFunction('reminder')\" @longpress=\"showFunctionDetail('reminder')\">\n            <view class=\"function-icon reminder\">⏰</view>\n            <text class=\"function-name\">任务提醒</text>\n            <text class=\"function-desc\">生日·闹钟·事项</text>\n          </view>\n          <view class=\"function-item\" @tap=\"openFunction('game')\" @longpress=\"showFunctionDetail('game')\">\n            <view class=\"function-icon game\">🎮</view>\n            <text class=\"function-name\">游戏娱乐</text>\n            <text class=\"function-desc\">猜谜·接龙·问答</text>\n          </view>\n          <view class=\"function-item\" @tap=\"openFunction('health')\" @longpress=\"showFunctionDetail('health')\">\n            <view class=\"function-icon health\">💪</view>\n            <text class=\"function-name\">健康管理</text>\n            <text class=\"function-desc\">运动·饮食·睡眠</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 快捷功能区域 -->\n      <view class=\"quick-actions\">\n        <view class=\"action-item\" @tap=\"openFunction('photo-search')\" @longpress=\"showFunctionDetail('photo-search')\">\n          <view class=\"action-icon\">📷</view>\n          <text class=\"action-text\">拍照搜题</text>\n        </view>\n        <view class=\"action-item\" @tap=\"openFunction('video-call')\" @longpress=\"showFunctionDetail('video-call')\">\n          <view class=\"action-icon\">📹</view>\n          <text class=\"action-text\">视频通话</text>\n        </view>\n        <view class=\"action-item\" @tap=\"openFunction('exam')\" @longpress=\"showFunctionDetail('exam')\">\n          <view class=\"action-icon\">📝</view>\n          <text class=\"action-text\">模拟考试</text>\n        </view>\n        <view class=\"action-item\" @tap=\"openFunction('textbook')\" @longpress=\"showFunctionDetail('textbook')\">\n          <view class=\"action-icon\">📖</view>\n          <text class=\"action-text\">课本学习</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部导航栏 -->\n    <view class=\"bottom-navigation\">\n      <view class=\"nav-item active\">\n        <text class=\"nav-icon\">🏠</text>\n        <text class=\"nav-text\">首页</text>\n      </view>\n\n      <!-- 美化的拍照搜题按钮 -->\n      <view class=\"camera-section\">\n        <view class=\"camera-btn\" @tap=\"showCameraInfo\">\n          <view class=\"camera-icon\">📷</view>\n        </view>\n        <text class=\"camera-text\">拍照搜题</text>\n      </view>\n\n      <view class=\"nav-item\" @tap=\"goToProfile\">\n        <text class=\"nav-icon\">👤</text>\n        <text class=\"nav-text\">我的</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      bannerList: [\n        {\n          title: '欢迎使用AI智能家教',\n          desc: '开启智能学习新体验',\n          icon: '🎓',\n          bgColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n        },\n        {\n          title: 'AI语音对话',\n          desc: '智能语音交互，随时随地学习',\n          icon: '🎤',\n          bgColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'\n        },\n        {\n          title: '个性化学习',\n          desc: '量身定制学习计划',\n          icon: '📚',\n          bgColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'\n        }\n      ]\n    }\n  },\n  methods: {\n    // 跳转到个人中心\n    goToProfile() {\n      uni.switchTab({\n        url: '/pages/ai-home/ai-home'\n      });\n    },\n\n    // 拍照搜题提示\n    showCameraInfo() {\n      uni.showToast({\n        title: '拍照搜题功能开发中',\n        icon: 'none',\n        duration: 2000\n      });\n    },\n\n    // AI对话功能\n    startVoiceChat() {\n      uni.showToast({\n        title: '正在启动AI对话...',\n        icon: 'loading'\n      });\n      // TODO: 实现语音对话功能\n    },\n\n    // 功能跳转方法\n    openFunction(type) {\n      const functionRoutes = {\n        'knowledge': '/pages/ai-knowledge/ai-knowledge',\n        'search': '/pages/ai-search/ai-search',\n        'writing': '/pages/ai-writing/ai-writing',\n        'translate': '/pages/ai-translate/ai-translate',\n        'emotion': '/pages/ai-emotion/ai-emotion',\n        'recommend': '/pages/ai-recommend/ai-recommend',\n        'reminder': '/pages/ai-reminder/ai-reminder',\n        'game': '/pages/ai-game/ai-game',\n        'health': '/pages/ai-health/ai-health',\n        'photo-search': '/pages/ai-photo/ai-photo',\n        'video-call': '/pages/ai-video/ai-video',\n        'exam': '/pages/ai-exam/ai-exam',\n        'textbook': '/pages/ai-textbook/ai-textbook'\n      };\n\n      const route = functionRoutes[type];\n      if (route) {\n        // 检查页面是否存在\n        const availablePages = ['knowledge', 'search', 'writing', 'translate', 'emotion', 'recommend'];\n        if (availablePages.includes(type)) {\n          uni.navigateTo({\n            url: route,\n            fail: (err) => {\n              console.error('页面跳转失败:', err);\n              uni.showToast({\n                title: '页面跳转失败',\n                icon: 'none'\n              });\n            }\n          });\n        } else {\n          uni.showToast({\n            title: '功能开发中，敬请期待',\n            icon: 'none'\n          });\n        }\n      } else {\n        uni.showToast({\n          title: '功能开发中，敬请期待',\n          icon: 'none'\n        });\n      }\n    },\n\n    // 显示功能详情\n    showFunctionDetail(type) {\n      const functionDetails = {\n        'knowledge': {\n          title: '知识问答',\n          icon: '📚',\n          description: '让AI能够回答各种问题，如历史、科学、技术、文化等方面的问题，就像一个知识渊博的助手。',\n          examples: [\n            '地球的直径是多少？',\n            '中国古代四大发明是什么？',\n            '光的传播速度是多少？'\n          ]\n        },\n        'search': {\n          title: '信息查询',\n          icon: '🔍',\n          description: '帮助学生查询各类信息，如天气、父母电话等实用信息。',\n          examples: [\n            '查询明天保定的天气',\n            '查询爸爸的电话号码',\n            '今天的新闻热点'\n          ]\n        },\n        'writing': {\n          title: '文本生成',\n          icon: '✍️',\n          description: '包括写作文章、故事、诗歌、摘要等各类文本创作。',\n          examples: [\n            '写一首关于春天的诗',\n            '写一个关于友谊的小故事',\n            '帮我写一篇关于环保的作文'\n          ]\n        },\n        'translate': {\n          title: '语言翻译',\n          icon: '🌐',\n          description: '实现不同语言之间的即时翻译，支持多种主流语言。',\n          examples: [\n            '把\"我喜欢振涛\"翻译成英语',\n            '这句日语是什么意思？',\n            '帮我翻译这段法语'\n          ]\n        },\n        'emotion': {\n          title: '情感陪伴',\n          icon: '💝',\n          description: '识别用户的情感状态，如高兴、生气、悲伤等，并给予相应的情感回应和安慰。',\n          examples: [\n            '我今天很难过',\n            '我考试考得不好',\n            '我今天吃了什么'\n          ]\n        },\n        'recommend': {\n          title: '智能推荐',\n          icon: '🎯',\n          description: '根据用户的兴趣和学习情况，推荐相关的学习内容、书籍、视频等。',\n          examples: [\n            '推荐一些数学学习资料',\n            '推荐适合我的英语电影',\n            '推荐一些科学实验'\n          ]\n        }\n      };\n\n      const detail = functionDetails[type];\n      if (detail) {\n        const exampleText = detail.examples.map(ex => `• ${ex}`).join('\\n');\n        uni.showModal({\n          title: `${detail.icon} ${detail.title}`,\n          content: `${detail.description}\\n\\n示例用法：\\n${exampleText}`,\n          showCancel: true,\n          cancelText: '取消',\n          confirmText: '立即体验',\n          success: (res) => {\n            if (res.confirm) {\n              this.openFunction(type);\n            }\n          }\n        });\n      }\n    }\n  }\n}\n</script>\n\n<style>\n/* 自定义状态栏 */\n.custom-status-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 88rpx;\n  background: #2c3e50;\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.status-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.app-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #fff;\n}\n\n.container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  padding-top: 108rpx;\n  padding-bottom: 140rpx;\n}\n\n/* 主要内容 */\n.main-content {\n  flex: 1;\n  padding: 20rpx;\n  padding-bottom: 120rpx;\n}\n\n/* 欢迎区域 */\n.welcome-section {\n  text-align: center;\n  padding: 60rpx 40rpx;\n  background: white;\n  border-radius: 20rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n}\n\n.welcome-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.welcome-desc {\n  font-size: 28rpx;\n  color: #7f8c8d;\n  display: block;\n}\n/* 轮播图样式 */\n.banner-section {\n  margin-bottom: 40rpx;\n}\n\n.banner-swiper {\n  height: 300rpx;\n  border-radius: 20rpx;\n  overflow: hidden;\n}\n\n.banner-item {\n  height: 100%;\n  border-radius: 20rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.banner-content {\n  position: absolute;\n  left: 40rpx;\n  top: 50%;\n  transform: translateY(-50%);\n  color: white;\n}\n\n.banner-title {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.banner-desc {\n  display: block;\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n.banner-icon {\n  position: absolute;\n  right: -200rpx;\n  top: -50rpx;\n  font-size: 200rpx;\n  opacity: 0.2;\n}\n\n/* AI对话模块 */\n.ai-chat-section {\n  margin-bottom: 40rpx;\n}\n\n.section-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.title-icon {\n  font-size: 32rpx;\n  margin-right: 10rpx;\n}\n\n.title-text {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.chat-card {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  display: flex;\n  align-items: center;\n  color: white;\n  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);\n}\n\n.voice-icon {\n  font-size: 60rpx;\n  margin-right: 30rpx;\n}\n\n.chat-content {\n  flex: 1;\n}\n\n.chat-title {\n  display: block;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.chat-desc {\n  display: block;\n  font-size: 26rpx;\n  opacity: 0.9;\n}\n\n.chat-arrow {\n  font-size: 40rpx;\n  opacity: 0.8;\n}\n/* 智能功能模块 */\n.functions-section {\n  margin-bottom: 40rpx;\n}\n\n.functions-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20rpx;\n}\n\n.function-item {\n  background: white;\n  border-radius: 20rpx;\n  padding: 30rpx 20rpx;\n  text-align: center;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.function-item:active {\n  transform: scale(0.95);\n}\n\n.function-icon {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 15rpx;\n  font-size: 40rpx;\n}\n\n.knowledge {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.search {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.writing {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.translate {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.emotion {\n  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\n}\n\n.recommend {\n  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n}\n\n.reminder {\n  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n}\n\n.game {\n  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);\n}\n\n.health {\n  background: linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%);\n}\n\n.function-name {\n  display: block;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n\n.function-desc {\n  display: block;\n  font-size: 22rpx;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 快捷功能区域 */\n.quick-actions {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 20rpx;\n  margin-bottom: 40rpx;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30rpx 15rpx;\n  background: white;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n}\n\n.action-item:active {\n  transform: scale(0.95);\n}\n\n.action-icon {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 50rpx;\n  margin-bottom: 15rpx;\n  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);\n  color: white;\n}\n\n.action-text {\n  font-size: 24rpx;\n  color: #333;\n  font-weight: 500;\n  text-align: center;\n}\n\n\n\n\n\n/* 底部导航栏 */\n.bottom-navigation {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  background: white;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  border-top: 1rpx solid #e5e5e5;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n/* 拍照搜题按钮区域 */\n.camera-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n}\n\n.camera-btn {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50rpx;\n  background: linear-gradient(135deg, #FF6B6B, #FF8E53);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);\n  transform: translateY(-10rpx);\n  transition: all 0.3s ease;\n  margin-bottom: 8rpx;\n}\n\n.camera-btn:active {\n  transform: translateY(-8rpx) scale(0.95);\n  box-shadow: 0 2rpx 10rpx rgba(255, 107, 107, 0.6);\n}\n\n.camera-icon {\n  font-size: 45rpx;\n  color: white;\n}\n\n.camera-text {\n  font-size: 20rpx;\n  color: #666;\n  font-weight: 500;\n  text-align: center;\n  transform: translateY(-10rpx);\n}\n\n.nav-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n  padding: 10rpx 0;\n  transition: all 0.3s ease;\n}\n\n.nav-item.active .nav-icon {\n  color: #007AFF;\n  transform: scale(1.1);\n}\n\n.nav-item.active .nav-text {\n  color: #007AFF;\n  font-weight: 600;\n}\n\n.nav-icon {\n  font-size: 40rpx;\n  margin-bottom: 8rpx;\n  color: #666;\n  transition: all 0.3s ease;\n}\n\n.nav-text {\n  font-size: 20rpx;\n  color: #666;\n  transition: all 0.3s ease;\n}\n</style>\n", "import MiniProgramPage from 'D:/study/study-project/s-sai/pages/home/<USER>'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAmJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,QACV;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IAEF;AAAA;AAAA,IAGD,aAAa,MAAM;AACjB,YAAM,iBAAiB;AAAA,QACrB,aAAa;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,QACX,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,YAAY;AAAA;AAGd,YAAM,QAAQ,eAAe,IAAI;AACjC,UAAI,OAAO;AAET,cAAM,iBAAiB,CAAC,aAAa,UAAU,WAAW,aAAa,WAAW,WAAW;AAC7F,YAAI,eAAe,SAAS,IAAI,GAAG;AACjCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,GAAG;AAC5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,aACK;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACvB,YAAM,kBAAkB;AAAA,QACtB,aAAa;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,UAAU;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,WAAW;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,aAAa;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,WAAW;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD,aAAa;AAAA,UACX,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA;AAGF,YAAM,SAAS,gBAAgB,IAAI;AACnC,UAAI,QAAQ;AACV,cAAM,cAAc,OAAO,SAAS,IAAI,QAAM,KAAK,EAAE,EAAE,EAAE,KAAK,IAAI;AAClEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK;AAAA,UACrC,SAAS,GAAG,OAAO,WAAW;AAAA;AAAA;AAAA,EAAc,WAAW;AAAA,UACvD,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACf,mBAAK,aAAa,IAAI;AAAA,YACxB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvUA,GAAG,WAAW,eAAe;"}