package cn.zhentao.im.controller;

import cn.zhentao.im.dto.ApiResponse;
import cn.zhentao.im.dto.MessageDto;
import cn.zhentao.im.dto.SendMessageRequest;
import cn.zhentao.im.service.MessageService;
import cn.zhentao.im.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息控制器
 * 处理消息相关的HTTP请求
 */
@Slf4j
@RestController
@RequestMapping("/api/im/message")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class MessageController {

    private final MessageService messageService;
    private final JwtUtil jwtUtil;

    /**
     * 发送消息
     * POST /api/im/message/send
     */
    @PostMapping("/send")
    public ApiResponse<MessageDto> sendMessage(
            @RequestHeader("Authorization") String token,
            @RequestBody SendMessageRequest request) {
        try {
            // 从token中获取发送者ID
            String actualToken = token.replace("Bearer ", "");
            Long fromUserId = jwtUtil.getUserIdFromToken(actualToken);

            MessageDto message = messageService.sendMessage(
                    fromUserId, 
                    request.getToUserId(), 
                    request.getContent()
            );

            return ApiResponse.success(message);
        } catch (Exception e) {
            log.error("发送消息失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取聊天历史
     * GET /api/im/message/history/{userId}
     */
    @GetMapping("/history/{userId}")
    public ApiResponse<List<MessageDto>> getChatHistory(
            @RequestHeader("Authorization") String token,
            @PathVariable Long userId) {
        try {
            // 从token中获取当前用户ID
            String actualToken = token.replace("Bearer ", "");
            Long currentUserId = jwtUtil.getUserIdFromToken(actualToken);

            List<MessageDto> messages = messageService.getChatHistory(currentUserId, userId);
            return ApiResponse.success(messages);
        } catch (Exception e) {
            log.error("获取聊天历史失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取用户收到的消息
     * GET /api/im/message/received
     */
    @GetMapping("/received")
    public ApiResponse<List<MessageDto>> getReceivedMessages(
            @RequestHeader("Authorization") String token) {
        try {
            // 从token中获取用户ID
            String actualToken = token.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(actualToken);

            List<MessageDto> messages = messageService.getMessagesForUser(userId);
            return ApiResponse.success(messages);
        } catch (Exception e) {
            log.error("获取接收消息失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }
}
