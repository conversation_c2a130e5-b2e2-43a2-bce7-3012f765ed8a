package com.zhentao.im.service;

import org.springframework.stereotype.Service;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内存用户状态服务
 * 当Redis不可用时的备用方案
 */
@Service
public class InMemoryUserStatusService {

    // 线程安全的HashMap，存储用户在线状态
    private final ConcurrentHashMap<Long, Long> onlineUsers = new ConcurrentHashMap<>();

    // 定时任务执行器
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    public InMemoryUserStatusService() {
        // 每小时清理过期的在线状态
        scheduler.scheduleAtFixedRate(this::cleanExpiredUsers, 1, 1, TimeUnit.HOURS);
    }

    /**
     * 设置用户在线
     * @param userId 用户ID
     */
    public void setUserOnline(Long userId) {
        // 存储用户ID和过期时间（24小时后）
        onlineUsers.put(userId, System.currentTimeMillis() + TimeUnit.HOURS.toMillis(24));
    }

    /**
     * 设置用户离线
     * @param userId 用户ID
     */
    public void setUserOffline(Long userId) {
        onlineUsers.remove(userId);
    }

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return true-在线，false-离线
     */
    public boolean isUserOnline(Long userId) {
        Long expireTime = onlineUsers.get(userId);
        if (expireTime == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() > expireTime) {
            onlineUsers.remove(userId);
            return false;
        }

        return true;
    }

    /**
     * 清理过期的用户状态
     */
    private void cleanExpiredUsers() {
        long currentTime = System.currentTimeMillis();
        onlineUsers.entrySet().removeIf(entry -> currentTime > entry.getValue());
    }
}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           package com.zhentao.im.service;

import com.zhentao.im.dto.MessageDto;
import com.zhentao.im.entity.Message;
import com.zhentao.im.entity.User;
import com.zhentao.im.repository.MessageRepository;
import com.zhentao.im.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息业务逻辑服务类
 */
@Service
@RequiredArgsConstructor
public class MessageService {

    private final MessageRepository messageRepository;
    private final UserRepository userRepository;

    /**
     * 保存消息到数据库
     * @param message 消息对象
     * @return 保存后的消息
     */
    public Message saveMessage(Message message) {
        return messageRepository.save(message);
    }

    /**
     * 发送消息
     * @param fromUserId 发送者ID
     * @param toUserId 接收者ID
     * @param content 消息内容
     * @return 消息DTO
     */
    public MessageDto sendMessage(Long fromUserId, Long toUserId, String content) {
        // 1. 创建消息实体
        Message message = new Message();
        message.setFromUserId(fromUserId);
        message.setToUserId(toUserId);
        message.setContent(content);
        message.setType(Message.MessageType.TEXT);
        message.setStatus(Message.MessageStatus.SENT);

        // 2. 保存到数据库
        Message savedMessage = messageRepository.save(message);

        // 3. 转换为DTO并返回
        return convertToDto(savedMessage);
    }

    /**
     * 获取两个用户之间的聊天历史
     * @param userId1 用户1的