package cn.zhentao.im.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * JWT工具类
 * 用于生成和验证JWT令牌
 */
@Component
public class JwtUtil {

    @Value("${jwt.secret}")         // 从配置文件读取密钥
    private String secret;

    @Value("${jwt.expiration}")     // 从配置文件读取过期时间
    private Long expiration;

    /**
     * 生成JWT令牌
     * @param userId 用户ID
     * @param username 用户名
     * @return JWT令牌字符串
     */
    public String generateToken(Long userId, String username) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return JWT.create()
                .withSubject(username)                  // 主题（用户名）
                .withClaim("userId", userId)            // 自定义声明（用户ID）
                .withIssuedAt(now)                      // 签发时间
                .withExpiresAt(expiryDate)              // 过期时间
                .sign(Algorithm.HMAC256(secret));       // 签名算法
    }

    /**
     * 验证JWT令牌
     * @param token JWT令牌
     * @return 解码后的JWT对象
     */
    public DecodedJWT verifyToken(String token) {
        try {
            return JWT.require(Algorithm.HMAC256(secret))
                    .build()
                    .verify(token);
        } catch (JWTVerificationException e) {
            throw new RuntimeException("无效的JWT令牌", e);
        }
    }

    /**
     * 从令牌中获取用户名
     */
    public String getUsernameFromToken(String token) {
        return verifyToken(token).getSubject();
    }

    /**
     * 从令牌中获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        return verifyToken(token).getClaim("userId").asLong();
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = verifyToken(token).getExpiresAt();
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }
}
