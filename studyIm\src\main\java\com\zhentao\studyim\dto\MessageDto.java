package com.zhentao.studyim.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息数据传输对象
 * 用于前后端消息传输
 */
@Data
public class MessageDto {
    private Long id;                    // 消息ID
    private Long fromUserId;            // 发送者ID
    private Long toUserId;              // 接收者ID
    private String content;             // 消息内容
    private String type;                // 消息类型
    private LocalDateTime sendTime;     // 发送时间
    private String fromUsername;        // 发送者用户名
    private String toUsername;          // 接收者用户名
}