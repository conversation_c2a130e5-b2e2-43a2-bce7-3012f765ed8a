2025-07-30T10:32:15.126+08:00  INFO 60192 --- [main] cn.zhentao.AppServiceApplication         : Starting AppServiceApplication using Java 17.0.12 with PID 60192 (D:\study\study-project\AiApp-service\target\classes started by 86177 in D:\study\study-project)
2025-07-30T10:32:15.127+08:00 DEBUG 60192 --- [main] cn.zhentao.AppServiceApplication         : Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-30T10:32:15.127+08:00  INFO 60192 --- [main] cn.zhentao.AppServiceApplication         : The following 1 profile is active: "dev"
2025-07-30T10:32:15.795+08:00  INFO 60192 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8082 (http)
2025-07-30T10:32:15.803+08:00  INFO 60192 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-30T10:32:15.803+08:00  INFO 60192 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-30T10:32:15.893+08:00  INFO 60192 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-30T10:32:15.894+08:00  INFO 60192 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 733 ms
2025-07-30T10:32:16.069+08:00 DEBUG 60192 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-30T10:32:16.120+08:00 DEBUG 60192 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 34 mappings in 'requestMappingHandlerMapping'
2025-07-30T10:32:16.129+08:00 DEBUG 60192 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-30T10:32:16.132+08:00 DEBUG 60192 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-30T10:32:16.209+08:00  INFO 60192 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8082 (http) with context path ''
2025-07-30T10:32:16.214+08:00  INFO 60192 --- [main] cn.zhentao.AppServiceApplication         : Started AppServiceApplication in 1.397 seconds (process running for 2.026)
2025-07-30T10:36:03.778+08:00  INFO 60192 --- [http-nio-8082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30T10:36:03.779+08:00  INFO 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-30T10:36:03.779+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-07-30T10:36:03.779+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-07-30T10:36:03.779+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-07-30T10:36:03.780+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7ac4b4d7
2025-07-30T10:36:03.781+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@20865e1b
2025-07-30T10:36:03.782+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-30T10:36:03.783+08:00  INFO 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-07-30T10:36:03.790+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : POST "/api/ai/miniprogram/info/query", parameters={}
2025-07-30T10:36:03.816+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to cn.zhentao.controller.MiniProgramAiController#informationQuery(Map)
2025-07-30T10:36:03.874+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [{userId=1950384608029495300, query=今天保定天气怎么样}]
2025-07-30T10:36:03.878+08:00  INFO 60192 --- [http-nio-8082-exec-1] c.zhentao.service.AiConversationService  : 信息查询请求 - 用户ID: 1950384608029495300, 查询: 今天保定天气怎么样
2025-07-30T10:36:03.903+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] c.a.d.p.okhttp.OkHttpClientFactory       : [connectionPool Config] connectionPoolSize: 32
2025-07-30T10:36:03.945+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] c.a.d.p.okhttp.OkHttpClientFactory       : [connectionPool Config] maxRequests: 32
2025-07-30T10:36:03.945+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] c.a.d.p.okhttp.OkHttpClientFactory       : [connectionPool Config] maxRequestsPerHost: 32
2025-07-30T10:36:17.573+08:00  INFO 60192 --- [http-nio-8082-exec-1] cn.zhentao.util.DashScopeAiUtil          : AI会话调用成功，prompt: 作为一个信息查询助手，请帮助查询以下信息（如果是天气查询，请说明需要具体城市；如果是电话查询，请提供相关建议）：今天保定天气怎么样, sessionId: 9b6c6149473042c19d144a3d511bfb87, response: 今天保定的天气情况如下：

- **当前温度**：30摄氏度
- **天气状况**：晴
- **最高温度**：32摄氏度
- **最低温度**：22摄氏度
- **风向**：东北风
- **风力**：3级

这是基于现有信息给出的预报，出门前建议再次确认最新天气情况。如果需要进一步的帮助或者更多细节，请告诉我！
2025-07-30T10:36:17.591+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-07-30T10:36:17.592+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Writing [cn.zhentao.common.Result@173c4726]
2025-07-30T10:36:17.607+08:00 DEBUG 60192 --- [http-nio-8082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-07-30T14:00:05.744+08:00  INFO 60136 --- [main] cn.zhentao.AppServiceApplication         : Starting AppServiceApplication using Java 17.0.12 with PID 60136 (D:\study\study-project\AiApp-service\target\classes started by 86177 in D:\study\study-project)
2025-07-30T14:00:05.745+08:00 DEBUG 60136 --- [main] cn.zhentao.AppServiceApplication         : Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-30T14:00:05.745+08:00  INFO 60136 --- [main] cn.zhentao.AppServiceApplication         : The following 1 profile is active: "dev"
2025-07-30T14:00:06.553+08:00  INFO 60136 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8082 (http)
2025-07-30T14:00:06.559+08:00  INFO 60136 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-30T14:00:06.560+08:00  INFO 60136 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-30T14:00:06.660+08:00  INFO 60136 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-30T14:00:06.660+08:00  INFO 60136 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 872 ms
2025-07-30T14:00:06.894+08:00 DEBUG 60136 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-30T14:00:06.973+08:00 DEBUG 60136 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 34 mappings in 'requestMappingHandlerMapping'
2025-07-30T14:00:06.984+08:00 DEBUG 60136 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-30T14:00:06.988+08:00 DEBUG 60136 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-30T14:00:07.088+08:00  INFO 60136 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8082 (http) with context path ''
2025-07-30T14:00:07.098+08:00  INFO 60136 --- [main] cn.zhentao.AppServiceApplication         : Started AppServiceApplication in 1.691 seconds (process running for 2.27)
2025-07-30T19:55:34.567+08:00  INFO 19248 --- [main] cn.zhentao.AppServiceApplication         : Starting AppServiceApplication using Java 17.0.12 with PID 19248 (D:\study\studyProject\study-project\AiApp-service\target\classes started by 86177 in D:\study\studyProject\study-project)
2025-07-30T19:55:34.568+08:00 DEBUG 19248 --- [main] cn.zhentao.AppServiceApplication         : Running with Spring Boot v3.1.5, Spring v6.0.13
2025-07-30T19:55:34.569+08:00  INFO 19248 --- [main] cn.zhentao.AppServiceApplication         : The following 1 profile is active: "dev"
2025-07-30T19:55:35.283+08:00  INFO 19248 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8082 (http)
2025-07-30T19:55:35.290+08:00  INFO 19248 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-30T19:55:35.290+08:00  INFO 19248 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-07-30T19:55:35.355+08:00  INFO 19248 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-30T19:55:35.355+08:00  INFO 19248 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 748 ms
2025-07-30T19:55:35.543+08:00 DEBUG 19248 --- [main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-30T19:55:35.600+08:00 DEBUG 19248 --- [main] s.w.s.m.m.a.RequestMappingHandlerMapping : 34 mappings in 'requestMappingHandlerMapping'
2025-07-30T19:55:35.609+08:00 DEBUG 19248 --- [main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-30T19:55:35.613+08:00 DEBUG 19248 --- [main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-30T19:55:35.678+08:00  INFO 19248 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8082 (http) with context path ''
2025-07-30T19:55:35.683+08:00  INFO 19248 --- [main] cn.zhentao.AppServiceApplication         : Started AppServiceApplication in 1.469 seconds (process running for 1.987)
