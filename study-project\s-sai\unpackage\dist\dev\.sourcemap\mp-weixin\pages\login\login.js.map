{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <view class=\"header\">\r\n      <view class=\"logo\">🔍</view>\r\n      <view class=\"title\">人脸登录</view>\r\n      <view class=\"subtitle\">请拍照或上传人脸照片进行识别登录</view>\r\n    </view>\r\n    \r\n    <view class=\"card\">\r\n      <view class=\"camera-section\">\r\n        <view class=\"camera-area\" @tap=\"chooseImage\">\r\n          <view v-if=\"!loginImage\" class=\"camera-placeholder\">\r\n            <view class=\"camera-icon\">📸</view>\r\n            <text class=\"camera-text\">点击拍照或上传图片</text>\r\n            <text class=\"camera-hint\">支持实时人脸识别</text>\r\n          </view>\r\n          <image v-else :src=\"loginImage\" class=\"captured-image\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <view v-if=\"loginLoading\" class=\"loading-section\">\r\n        <view class=\"loading-container\">\r\n          <view class=\"loading-spinner\"></view>\r\n          <text class=\"loading-text\">正在识别中...</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <view v-if=\"loginResult\" class=\"result-section\">\r\n        <view class=\"result-message\">\r\n          {{ loginResult }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"footer\">\r\n      <text class=\"footer-text\">还没有账号？</text>\r\n      <text class=\"register-link\" @tap=\"goToRegister\">立即注册</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      loginImage: '',\r\n      loginLoading: false,\r\n      loginResult: ''\r\n    }\r\n  },\r\n  methods: {\r\n    chooseImage() {\r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album', 'camera'],\r\n        success: res => {\r\n          this.loginImage = res.tempFilePaths[0];\r\n          this.loginResult = '';\r\n          this.submitLogin();\r\n        }\r\n      });\r\n    },\r\n    \r\n    submitLogin() {\r\n      if (!this.loginImage) {\r\n        this.loginResult = '请拍照或上传人脸图片';\r\n        return;\r\n      }\r\n      \r\n      this.loginLoading = true;\r\n      this.loginResult = '';\r\n      \r\n      uni.getFileSystemManager().readFile({\r\n        filePath: this.loginImage,\r\n        encoding: 'base64',\r\n        success: res => {\r\n          const base64 = 'data:image/jpeg;base64,' + res.data;\r\n          uni.request({\r\n            url: 'http://localhost:8081/fact-info/check',\r\n            method: 'POST',\r\n            header: {\r\n              'Content-Type': 'application/json'\r\n            },\r\n            data: {\r\n              imageData: base64\r\n            },\r\n            success: r => {\r\n              const data = r.data;\r\n              if (data.code === 200) {\r\n                this.loginResult = '✅ 登录成功！欢迎回来，' + data.userInfo.name;\r\n                // 存储用户信息\r\n                uni.setStorageSync('userInfo', data.userInfo);\r\n                setTimeout(() => {\r\n                  uni.showToast({\r\n                    title: '登录成功',\r\n                    icon: 'success',\r\n                    duration: 1500\r\n                  });\r\n                  // 跳转到首页\r\n                  setTimeout(() => {\r\n                    uni.reLaunch({\r\n                      url: '/pages/home/<USER>'\r\n                    });\r\n                  }, 1500);\r\n                }, 500);\r\n              } else {\r\n                this.loginResult = '❌ 登录失败：' + data.message;\r\n              }\r\n            },\r\n            fail: err => {\r\n              this.loginResult = '❌ 登录失败：' + err.errMsg;\r\n            },\r\n            complete: () => {\r\n              this.loginLoading = false;\r\n            }\r\n          });\r\n        },\r\n        fail: err => {\r\n          this.loginResult = '❌ 图片读取失败：' + err.errMsg;\r\n          this.loginLoading = false;\r\n        }\r\n      });\r\n    },\r\n    \r\n    goToRegister() {\r\n      uni.navigateTo({\r\n        url: '/pages/register/register'\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  padding: 40rpx 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.container::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\r\n  opacity: 0.3;\r\n  pointer-events: none;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 60rpx;\r\n  padding-top: 60rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.logo {\r\n  font-size: 100rpx;\r\n  margin-bottom: 30rpx;\r\n  animation: float 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% { transform: translateY(0px); }\r\n  50% { transform: translateY(-10px); }\r\n}\r\n\r\n.title {\r\n  font-size: 48rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  letter-spacing: 2rpx;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 28rpx;\r\n  color: #6c757d;\r\n  line-height: 1.5;\r\n}\r\n\r\n.card {\r\n  background: #ffffff;\r\n  border-radius: 16rpx;\r\n  padding: 40rpx 32rpx;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n  border: 1rpx solid #e9ecef;\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.camera-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.camera-area {\r\n  border: 2rpx dashed #dee2e6;\r\n  border-radius: 12rpx;\r\n  padding: 60rpx 20rpx;\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.camera-area::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(118, 75, 162, 0.1), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.camera-area:active::before {\r\n  left: 100%;\r\n}\r\n\r\n.camera-area:active {\r\n  border-color: #2c3e50;\r\n  background: #e9ecef;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.camera-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.camera-icon {\r\n  font-size: 80rpx;\r\n  margin-bottom: 20rpx;\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n.camera-text {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.camera-hint {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.captured-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  border-radius: 20rpx;\r\n  border: 4rpx solid #e1e5e9;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n  animation: fadeIn 0.5s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: scale(0.8); }\r\n  to { opacity: 1; transform: scale(1); }\r\n}\r\n\r\n.loading-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40rpx 0;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border: 6rpx solid #e9ecef;\r\n  border-top: 6rpx solid #2c3e50;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.loading-text {\r\n  font-size: 32rpx;\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n.result-section {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.result-message {\r\n  padding: 24rpx 32rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  text-align: center;\r\n  background: #2c3e50;\r\n  color: #fff;\r\n  box-shadow: 0 4rpx 12rpx rgba(44, 62, 80, 0.2);\r\n  line-height: 1.5;\r\n  animation: slideIn 0.5s ease;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from { opacity: 0; transform: translateY(20rpx); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.footer {\r\n  text-align: center;\r\n  margin-top: auto;\r\n  padding-bottom: 40rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.footer-text {\r\n  font-size: 28rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.register-link {\r\n  font-size: 28rpx;\r\n  color: #2c3e50;\r\n  font-weight: 600;\r\n  margin-left: 12rpx;\r\n  text-decoration: underline;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.register-link:active {\r\n  transform: scale(0.95);\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/study/study-project/s-sai/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA0CA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,aAAa;AAAA,IACf;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,SAAO;AACd,eAAK,aAAa,IAAI,cAAc,CAAC;AACrC,eAAK,cAAc;AACnB,eAAK,YAAW;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,cAAc;AACZ,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,cAAc;AACnB;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,cAAc;AAEnBA,0BAAI,qBAAsB,EAAC,SAAS;AAAA,QAClC,UAAU,KAAK;AAAA,QACf,UAAU;AAAA,QACV,SAAS,SAAO;AACd,gBAAM,SAAS,4BAA4B,IAAI;AAC/CA,wBAAAA,MAAI,QAAQ;AAAA,YACV,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,gBAAgB;AAAA,YACjB;AAAA,YACD,MAAM;AAAA,cACJ,WAAW;AAAA,YACZ;AAAA,YACD,SAAS,OAAK;AACZ,oBAAM,OAAO,EAAE;AACf,kBAAI,KAAK,SAAS,KAAK;AACrB,qBAAK,cAAc,iBAAiB,KAAK,SAAS;AAElDA,8BAAAA,MAAI,eAAe,YAAY,KAAK,QAAQ;AAC5C,2BAAW,MAAM;AACfA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,oBACN,UAAU;AAAA,kBACZ,CAAC;AAED,6BAAW,MAAM;AACfA,kCAAAA,MAAI,SAAS;AAAA,sBACX,KAAK;AAAA,oBACP,CAAC;AAAA,kBACF,GAAE,IAAI;AAAA,gBACR,GAAE,GAAG;AAAA,qBACD;AACL,qBAAK,cAAc,YAAY,KAAK;AAAA,cACtC;AAAA,YACD;AAAA,YACD,MAAM,SAAO;AACX,mBAAK,cAAc,YAAY,IAAI;AAAA,YACpC;AAAA,YACD,UAAU,MAAM;AACd,mBAAK,eAAe;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACF;AAAA,QACD,MAAM,SAAO;AACX,eAAK,cAAc,cAAc,IAAI;AACrC,eAAK,eAAe;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;AClIA,GAAG,WAAW,eAAe;"}