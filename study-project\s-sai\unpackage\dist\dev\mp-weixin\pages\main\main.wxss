
.main-container.data-v-4f50ca8f {
  min-height: 100vh;
  background: #f8f9fa;
}
.page-content.data-v-4f50ca8f {
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
}

/* 占位页面样式 */
.placeholder-page.data-v-4f50ca8f {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  padding: 40rpx;
}
.placeholder-icon.data-v-4f50ca8f {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}
.placeholder-title.data-v-4f50ca8f {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.placeholder-desc.data-v-4f50ca8f {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
