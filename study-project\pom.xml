<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.zhentao</groupId>
    <artifactId>AiTutor</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>Ai-common</module>
        <module>Ai-gateway</module>
        <module>Ai-userlogin</module>
        <module>AiApp-service</module>
        <module>AiPC-service</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>17</java.version>
        <spring-boot.version>3.1.5</spring-boot.version>
        <spring-cloud.version>2022.0.4</spring-cloud.version>
        <spring-cloud-alibaba.version>2022.0.0.0</spring-cloud-alibaba.version>
        <opencv.version>4.7.0-1.5.9</opencv.version>
        <javacv.version>1.5.9</javacv.version>
        <deeplearning4j.version>1.0.0-M2.1</deeplearning4j.version>
        <tensorflow.version>0.5.0</tensorflow.version>
        <onnx.version>1.15.0</onnx.version>
        <netty.version>4.1.104.Final</netty.version>
        <jwt.version>4.4.0</jwt.version>
        <fastjson2.version>2.0.43</fastjson2.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud Alibaba BOM -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- JavaCV for computer vision -->
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>javacv</artifactId>
                <version>${javacv.version}</version>
            </dependency>

            <!-- JavaCV Platform -->
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>javacv-platform</artifactId>
                <version>${javacv.version}</version>
            </dependency>

            <!-- OpenCV核心库 -->
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>opencv</artifactId>
                <version>${opencv.version}</version>
            </dependency>

            <!-- OpenCV Platform -->
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>opencv-platform</artifactId>
                <version>${opencv.version}</version>
            </dependency>

            <!-- OpenCV平台特定库 -->
            <dependency>
                <groupId>org.bytedeco</groupId>
                <artifactId>opencv</artifactId>
                <version>${opencv.version}</version>
                <classifier>windows-x86_64</classifier>
            </dependency>

            <!-- DeepLearning4J for AI/ML -->
            <dependency>
                <groupId>org.deeplearning4j</groupId>
                <artifactId>deeplearning4j-core</artifactId>
                <version>${deeplearning4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.nd4j</groupId>
                <artifactId>nd4j-native-platform</artifactId>
                <version>${deeplearning4j.version}</version>
            </dependency>

            <!-- TensorFlow Java -->
            <dependency>
                <groupId>org.tensorflow</groupId>
                <artifactId>tensorflow-core-platform</artifactId>
                <version>${tensorflow.version}</version>
            </dependency>

            <!-- ONNX Runtime for AI model inference -->
            <dependency>
                <groupId>com.microsoft.onnxruntime</groupId>
                <artifactId>onnxruntime</artifactId>
                <version>${onnx.version}</version>
            </dependency>

            <!-- Face recognition specific libraries -->
            <dependency>
                <groupId>com.github.sarxos</groupId>
                <artifactId>webcam-capture</artifactId>
                <version>0.3.12</version>
            </dependency>

            <!-- AI framework dependencies -->
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j</artifactId>
                <version>0.24.0</version>
            </dependency>

            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-spring-boot-starter</artifactId>
                <version>0.24.0</version>
            </dependency>

            <!-- OpenAI Java SDK -->
            <dependency>
                <groupId>com.theokanning.openai-gpt3-java</groupId>
                <artifactId>service</artifactId>
                <version>0.18.2</version>
            </dependency>

            <!-- JSON processing -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.15.2</version>
            </dependency>

            <!-- Netty网络框架 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <!-- JWT身份认证 -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- FastJSON2 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
