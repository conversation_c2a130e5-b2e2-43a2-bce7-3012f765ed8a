{"version": 3, "file": "ai-translate.js", "sources": ["pages/ai-translate/ai-translate.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWktdHJhbnNsYXRlL2FpLXRyYW5zbGF0ZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-icon\">🌐</view>\r\n      <view class=\"header-info\">\r\n        <text class=\"header-title\">语言翻译</text>\r\n        <text class=\"header-desc\">多语言互译，支持主流语言</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 语言选择区域 -->\r\n    <view class=\"language-selector\">\r\n      <view class=\"language-item\">\r\n        <text class=\"language-label\">源语言</text>\r\n        <picker @change=\"onFromLangChange\" :value=\"fromLangIndex\" :range=\"languages\" range-key=\"name\">\r\n          <view class=\"language-picker\">\r\n            <text class=\"language-text\">{{ languages[fromLangIndex].name }}</text>\r\n            <text class=\"picker-arrow\">▼</text>\r\n          </view>\r\n        </picker>\r\n      </view>\r\n      \r\n      <view class=\"swap-btn\" @tap=\"swapLanguages\">\r\n        <text class=\"swap-icon\">⇄</text>\r\n      </view>\r\n      \r\n      <view class=\"language-item\">\r\n        <text class=\"language-label\">目标语言</text>\r\n        <picker @change=\"onToLangChange\" :value=\"toLangIndex\" :range=\"languages\" range-key=\"name\">\r\n          <view class=\"language-picker\">\r\n            <text class=\"language-text\">{{ languages[toLangIndex].name }}</text>\r\n            <text class=\"picker-arrow\">▼</text>\r\n          </view>\r\n        </picker>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 翻译区域 -->\r\n    <view class=\"translate-area\">\r\n      <!-- 输入区域 -->\r\n      <view class=\"input-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">{{ languages[fromLangIndex].flag }} 输入文本</text>\r\n          <text class=\"char-count\">{{ inputText.length }}/1000</text>\r\n        </view>\r\n        <textarea \r\n          class=\"input-textarea\" \r\n          v-model=\"inputText\" \r\n          :placeholder=\"`请输入要翻译的${languages[fromLangIndex].name}文本...`\"\r\n          :maxlength=\"1000\"\r\n          @input=\"onInputChange\"\r\n        />\r\n        <view class=\"input-actions\">\r\n          <button class=\"action-btn clear\" @tap=\"clearInput\" v-if=\"inputText\">\r\n            <text class=\"btn-icon\">🗑️</text>\r\n            <text class=\"btn-text\">清空</text>\r\n          </button>\r\n          <button class=\"action-btn paste\" @tap=\"pasteText\">\r\n            <text class=\"btn-icon\">📋</text>\r\n            <text class=\"btn-text\">粘贴</text>\r\n          </button>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 翻译按钮 -->\r\n      <view class=\"translate-btn-section\">\r\n        <button \r\n          class=\"translate-btn\" \r\n          :class=\"{ disabled: !inputText.trim() || isTranslating }\" \r\n          @tap=\"translateText\"\r\n        >\r\n          <text class=\"btn-icon\">{{ isTranslating ? '⏳' : '🔄' }}</text>\r\n          <text class=\"btn-text\">{{ isTranslating ? '翻译中...' : '开始翻译' }}</text>\r\n        </button>\r\n      </view>\r\n\r\n      <!-- 输出区域 -->\r\n      <view class=\"output-section\">\r\n        <view class=\"section-header\">\r\n          <text class=\"section-title\">{{ languages[toLangIndex].flag }} 翻译结果</text>\r\n          <view v-if=\"translatedText && !isTranslating\" class=\"output-actions\">\r\n            <button class=\"action-btn copy\" @tap=\"copyResult\">\r\n              <text class=\"btn-icon\">📋</text>\r\n              <text class=\"btn-text\">复制</text>\r\n            </button>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 翻译中的动画 -->\r\n        <view v-if=\"isTranslating\" class=\"translating-animation\">\r\n          <view class=\"typing-indicator\">\r\n            <view class=\"dot\"></view>\r\n            <view class=\"dot\"></view>\r\n            <view class=\"dot\"></view>\r\n          </view>\r\n          <text class=\"translating-text\">AI正在翻译中...</text>\r\n        </view>\r\n        \r\n        <!-- 翻译结果 -->\r\n        <view v-else-if=\"translatedText\" class=\"output-content\">\r\n          <text class=\"output-text\">{{ translatedText }}</text>\r\n        </view>\r\n        \r\n        <!-- 空状态 -->\r\n        <view v-else class=\"empty-state\">\r\n          <text class=\"empty-icon\">🌐</text>\r\n          <text class=\"empty-text\">翻译结果将在这里显示</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快捷翻译 -->\r\n    <view v-if=\"!inputText && !translatedText\" class=\"quick-translate\">\r\n      <text class=\"quick-title\">💡 试试这些翻译</text>\r\n      <view class=\"quick-list\">\r\n        <view \r\n          v-for=\"(item, index) in quickTranslations\" \r\n          :key=\"index\" \r\n          class=\"quick-item\" \r\n          @tap=\"useQuickTranslation(item)\"\r\n        >\r\n          <text class=\"quick-text\">{{ item }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      inputText: '',\r\n      translatedText: '',\r\n      isTranslating: false,\r\n      userId: 123,\r\n      fromLangIndex: 1, // 默认中文\r\n      toLangIndex: 0,   // 默认英文\r\n      languages: [\r\n        { name: '英文', code: 'en', flag: '🇺🇸' },\r\n        { name: '中文', code: 'zh', flag: '🇨🇳' },\r\n        { name: '日文', code: 'ja', flag: '🇯🇵' },\r\n        { name: '韩文', code: 'ko', flag: '🇰🇷' },\r\n        { name: '法文', code: 'fr', flag: '🇫🇷' },\r\n        { name: '德文', code: 'de', flag: '🇩🇪' },\r\n        { name: '西班牙文', code: 'es', flag: '🇪🇸' },\r\n        { name: '俄文', code: 'ru', flag: '🇷🇺' }\r\n      ],\r\n      quickTranslations: [\r\n        'Hello, how are you?',\r\n        '我喜欢学习编程',\r\n        'Thank you very much',\r\n        '今天天气很好',\r\n        'Good morning',\r\n        '很高兴认识你'\r\n      ]\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadUserInfo();\r\n  },\r\n  methods: {\r\n    loadUserInfo() {\r\n      const userInfo = uni.getStorageSync('userInfo');\r\n      if (userInfo && userInfo.id) {\r\n        this.userId = userInfo.id;\r\n      }\r\n    },\r\n\r\n    onFromLangChange(e) {\r\n      this.fromLangIndex = e.detail.value;\r\n      this.clearResults();\r\n    },\r\n\r\n    onToLangChange(e) {\r\n      this.toLangIndex = e.detail.value;\r\n      this.clearResults();\r\n    },\r\n\r\n    swapLanguages() {\r\n      const temp = this.fromLangIndex;\r\n      this.fromLangIndex = this.toLangIndex;\r\n      this.toLangIndex = temp;\r\n      \r\n      // 交换输入和输出文本\r\n      const tempText = this.inputText;\r\n      this.inputText = this.translatedText;\r\n      this.translatedText = tempText;\r\n    },\r\n\r\n    onInputChange() {\r\n      this.translatedText = '';\r\n    },\r\n\r\n    clearInput() {\r\n      this.inputText = '';\r\n      this.translatedText = '';\r\n    },\r\n\r\n    clearResults() {\r\n      this.translatedText = '';\r\n    },\r\n\r\n    async pasteText() {\r\n      try {\r\n        const clipboardData = await uni.getClipboardData();\r\n        if (clipboardData.data) {\r\n          this.inputText = clipboardData.data;\r\n          uni.showToast({\r\n            title: '粘贴成功',\r\n            icon: 'success'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '粘贴失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n\r\n    useQuickTranslation(text) {\r\n      this.inputText = text;\r\n      this.translateText();\r\n    },\r\n\r\n    async translateText() {\r\n      if (!this.inputText.trim() || this.isTranslating) return;\r\n\r\n      this.isTranslating = true;\r\n      this.translatedText = '';\r\n\r\n      try {\r\n        const fromLang = this.languages[this.fromLangIndex].name;\r\n        const toLang = this.languages[this.toLangIndex].name;\r\n        \r\n        const response = await this.callTranslateAPI(this.inputText.trim(), fromLang, toLang);\r\n        \r\n        if (response && response.success) {\r\n          this.translatedText = response.response || '翻译失败，请重试。';\r\n        } else {\r\n          this.translatedText = '翻译服务暂时不可用，请稍后再试。';\r\n        }\r\n      } catch (error) {\r\n        this.translatedText = '网络连接失败，请检查网络后重试。';\r\n        console.error('API调用失败:', error);\r\n      }\r\n\r\n      this.isTranslating = false;\r\n    },\r\n\r\n    async callTranslateAPI(text, fromLang, toLang) {\r\n      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/translate';\r\n      \r\n      const response = await uni.request({\r\n        url: apiUrl,\r\n        method: 'POST',\r\n        header: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n          userId: this.userId,\r\n          text: text,\r\n          fromLang: fromLang,\r\n          toLang: toLang\r\n        }\r\n      });\r\n\r\n      if (response.statusCode === 200 && response.data.code === 200) {\r\n        return response.data.data;\r\n      } else {\r\n        throw new Error('API调用失败');\r\n      }\r\n    },\r\n\r\n    copyResult() {\r\n      if (!this.translatedText) return;\r\n      \r\n      uni.setClipboardData({\r\n        data: this.translatedText,\r\n        success: () => {\r\n          uni.showToast({\r\n            title: '复制成功',\r\n            icon: 'success'\r\n          });\r\n        },\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '复制失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #e9ecef;\r\n}\r\n\r\n.header-icon {\r\n  font-size: 48rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.header-info {\r\n  flex: 1;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  display: block;\r\n}\r\n\r\n.header-desc {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-top: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.language-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #e9ecef;\r\n}\r\n\r\n.language-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.language-label {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.language-picker {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #e9ecef;\r\n}\r\n\r\n.language-text {\r\n  font-size: 28rpx;\r\n  color: #2c3e50;\r\n  font-weight: 500;\r\n}\r\n\r\n.picker-arrow {\r\n  font-size: 20rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.swap-btn {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  background: #007bff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 30rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.swap-btn:active {\r\n  transform: scale(0.9);\r\n  background: #0056b3;\r\n}\r\n\r\n.swap-icon {\r\n  font-size: 32rpx;\r\n  color: #fff;\r\n  font-weight: bold;\r\n}\r\n\r\n.translate-area {\r\n  padding: 30rpx;\r\n}\r\n\r\n.input-section, .output-section {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.char-count {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.input-textarea {\r\n  width: 100%;\r\n  min-height: 200rpx;\r\n  padding: 20rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  font-size: 28rpx;\r\n  line-height: 1.5;\r\n  background: #f8f9fa;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.input-actions, .output-actions {\r\n  display: flex;\r\n  gap: 16rpx;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  padding: 12rpx 20rpx;\r\n  border-radius: 20rpx;\r\n  font-size: 24rpx;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn.clear {\r\n  background: #dc3545;\r\n  color: #fff;\r\n}\r\n\r\n.action-btn.paste {\r\n  background: #6c757d;\r\n  color: #fff;\r\n}\r\n\r\n.action-btn.copy {\r\n  background: #28a745;\r\n  color: #fff;\r\n}\r\n\r\n.action-btn:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n.translate-btn-section {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.translate-btn {\r\n  width: 100%;\r\n  height: 100rpx;\r\n  background: #007bff;\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 50rpx;\r\n  font-size: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 16rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.translate-btn.disabled {\r\n  background: #ccc;\r\n  color: #999;\r\n}\r\n\r\n.translate-btn:not(.disabled):active {\r\n  transform: scale(0.98);\r\n  background: #0056b3;\r\n}\r\n\r\n.translating-animation {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n}\r\n\r\n.typing-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.dot {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  border-radius: 50%;\r\n  background: #007bff;\r\n  animation: typing 1.4s infinite;\r\n}\r\n\r\n.dot:nth-child(2) { animation-delay: 0.2s; }\r\n.dot:nth-child(3) { animation-delay: 0.4s; }\r\n\r\n@keyframes typing {\r\n  0%, 60%, 100% { opacity: 0.3; transform: scale(1); }\r\n  30% { opacity: 1; transform: scale(1.2); }\r\n}\r\n\r\n.translating-text {\r\n  font-size: 28rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.output-content {\r\n  border: 1rpx solid #e9ecef;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.output-text {\r\n  font-size: 28rpx;\r\n  line-height: 1.8;\r\n  color: #333;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 64rpx;\r\n  margin-bottom: 20rpx;\r\n  opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n.quick-translate {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin: 30rpx;\r\n  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);\r\n}\r\n\r\n.quick-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.quick-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.quick-item {\r\n  padding: 20rpx;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.quick-item:active {\r\n  background: #e9ecef;\r\n  transform: scale(0.98);\r\n}\r\n\r\n.quick-text {\r\n  font-size: 26rpx;\r\n  color: #495057;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/study/study-project/s-sai/pages/ai-translate/ai-translate.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAkIA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,eAAe;AAAA;AAAA,MACf,aAAa;AAAA;AAAA,MACb,WAAW;AAAA,QACT,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,OAAQ;AAAA,QACxC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,OAAQ;AAAA,QACxC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,OAAQ;AAAA,QACxC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,OAAQ;AAAA,QACxC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,OAAQ;AAAA,QACxC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,OAAQ;AAAA,QACxC,EAAE,MAAM,QAAQ,MAAM,MAAM,MAAM,OAAQ;AAAA,QAC1C,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO;AAAA,MACxC;AAAA,MACD,mBAAmB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,aAAY;AAAA,EAClB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,YAAY,SAAS,IAAI;AAC3B,aAAK,SAAS,SAAS;AAAA,MACzB;AAAA,IACD;AAAA,IAED,iBAAiB,GAAG;AAClB,WAAK,gBAAgB,EAAE,OAAO;AAC9B,WAAK,aAAY;AAAA,IAClB;AAAA,IAED,eAAe,GAAG;AAChB,WAAK,cAAc,EAAE,OAAO;AAC5B,WAAK,aAAY;AAAA,IAClB;AAAA,IAED,gBAAgB;AACd,YAAM,OAAO,KAAK;AAClB,WAAK,gBAAgB,KAAK;AAC1B,WAAK,cAAc;AAGnB,YAAM,WAAW,KAAK;AACtB,WAAK,YAAY,KAAK;AACtB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAED,gBAAgB;AACd,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAED,aAAa;AACX,WAAK,YAAY;AACjB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAED,eAAe;AACb,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAED,MAAM,YAAY;AAChB,UAAI;AACF,cAAM,gBAAgB,MAAMA,oBAAI;AAChC,YAAI,cAAc,MAAM;AACtB,eAAK,YAAY,cAAc;AAC/BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA,IAED,oBAAoB,MAAM;AACxB,WAAK,YAAY;AACjB,WAAK,cAAa;AAAA,IACnB;AAAA,IAED,MAAM,gBAAgB;AACpB,UAAI,CAAC,KAAK,UAAU,KAAI,KAAM,KAAK;AAAe;AAElD,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AAEtB,UAAI;AACF,cAAM,WAAW,KAAK,UAAU,KAAK,aAAa,EAAE;AACpD,cAAM,SAAS,KAAK,UAAU,KAAK,WAAW,EAAE;AAEhD,cAAM,WAAW,MAAM,KAAK,iBAAiB,KAAK,UAAU,KAAM,GAAE,UAAU,MAAM;AAEpF,YAAI,YAAY,SAAS,SAAS;AAChC,eAAK,iBAAiB,SAAS,YAAY;AAAA,eACtC;AACL,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACA,SAAO,OAAO;AACd,aAAK,iBAAiB;AACtBA,sBAAc,MAAA,MAAA,SAAA,8CAAA,YAAY,KAAK;AAAA,MACjC;AAEA,WAAK,gBAAgB;AAAA,IACtB;AAAA,IAED,MAAM,iBAAiB,MAAM,UAAU,QAAQ;AAC7C,YAAM,SAAS;AAEf,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,MAAM;AAAA,UACJ,QAAQ,KAAK;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,KAAK;AAC7D,eAAO,SAAS,KAAK;AAAA,aAChB;AACL,cAAM,IAAI,MAAM,SAAS;AAAA,MAC3B;AAAA,IACD;AAAA,IAED,aAAa;AACX,UAAI,CAAC,KAAK;AAAgB;AAE1BA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,KAAK;AAAA,QACX,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvSA,GAAG,WAAW,eAAe;"}