{"version": 3, "file": "ai-emotion.js", "sources": ["pages/ai-emotion/ai-emotion.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWktZW1vdGlvbi9haS1lbW90aW9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-icon\">💝</view>\r\n      <view class=\"header-info\">\r\n        <text class=\"header-title\">情感陪伴</text>\r\n        <text class=\"header-desc\">情感识别回应，温暖陪伴</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 情感状态选择 -->\r\n    <view class=\"emotion-selector\">\r\n      <text class=\"selector-title\">💭 当前心情</text>\r\n      <view class=\"emotion-grid\">\r\n        <view \r\n          v-for=\"(emotion, index) in emotions\" \r\n          :key=\"index\"\r\n          class=\"emotion-item\"\r\n          :class=\"{ active: selectedEmotion === emotion.key }\"\r\n          @tap=\"selectEmotion(emotion.key)\"\r\n        >\r\n          <text class=\"emotion-icon\">{{ emotion.icon }}</text>\r\n          <text class=\"emotion-name\">{{ emotion.name }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 聊天区域 -->\r\n    <scroll-view class=\"chat-area\" scroll-y=\"true\" :scroll-top=\"scrollTop\" scroll-with-animation=\"true\">\r\n      <view class=\"message-list\">\r\n        <view v-for=\"(message, index) in messages\" :key=\"index\" class=\"message-item\" :class=\"message.type\">\r\n          <view class=\"message-avatar\">\r\n            <text class=\"avatar-text\">{{ message.type === 'user' ? '我' : '💝' }}</text>\r\n          </view>\r\n          <view class=\"message-content\">\r\n            <view class=\"message-bubble\">\r\n              <text class=\"message-text\">{{ message.content }}</text>\r\n              <text class=\"message-time\">{{ message.time }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- AI正在输入提示 -->\r\n        <view v-if=\"isAiTyping\" class=\"message-item ai typing\">\r\n          <view class=\"message-avatar\">\r\n            <text class=\"avatar-text\">💝</text>\r\n          </view>\r\n          <view class=\"message-content\">\r\n            <view class=\"message-bubble\">\r\n              <view class=\"typing-indicator\">\r\n                <view class=\"dot\"></view>\r\n                <view class=\"dot\"></view>\r\n                <view class=\"dot\"></view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n    <!-- 输入区域 -->\r\n    <view class=\"input-area\">\r\n      <view class=\"input-container\">\r\n        <input \r\n          class=\"input-field\" \r\n          v-model=\"inputText\" \r\n          placeholder=\"分享您的感受，我会用心倾听...\" \r\n          :disabled=\"isLoading\"\r\n          @confirm=\"sendMessage\"\r\n        />\r\n        <button \r\n          class=\"send-btn\" \r\n          :class=\"{ disabled: !inputText.trim() || isLoading }\" \r\n          @tap=\"sendMessage\"\r\n        >\r\n          {{ isLoading ? '发送中' : '发送' }}\r\n        </button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 情感建议 -->\r\n    <view v-if=\"messages.length === 0\" class=\"emotion-suggestions\">\r\n      <text class=\"suggestions-title\">💡 可以这样表达</text>\r\n      <view class=\"suggestions-list\">\r\n        <view \r\n          v-for=\"(suggestion, index) in currentSuggestions\" \r\n          :key=\"index\" \r\n          class=\"suggestion-item\" \r\n          @tap=\"useSuggestion(suggestion)\"\r\n        >\r\n          <text class=\"suggestion-text\">{{ suggestion }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      messages: [],\r\n      inputText: '',\r\n      isLoading: false,\r\n      isAiTyping: false,\r\n      scrollTop: 0,\r\n      userId: 123,\r\n      selectedEmotion: 'neutral',\r\n      emotions: [\r\n        { key: 'happy', name: '开心', icon: '😊' },\r\n        { key: 'sad', name: '难过', icon: '😢' },\r\n        { key: 'angry', name: '生气', icon: '😠' },\r\n        { key: 'worried', name: '担心', icon: '😰' },\r\n        { key: 'excited', name: '兴奋', icon: '🤩' },\r\n        { key: 'tired', name: '疲惫', icon: '😴' },\r\n        { key: 'confused', name: '困惑', icon: '😕' },\r\n        { key: 'neutral', name: '平静', icon: '😐' }\r\n      ],\r\n      emotionSuggestions: {\r\n        happy: [\r\n          '我今天很开心，因为...',\r\n          '有件好事想分享',\r\n          '心情特别好',\r\n          '感觉很幸福'\r\n        ],\r\n        sad: [\r\n          '我今天心情不太好',\r\n          '感觉有点难过',\r\n          '遇到了一些困难',\r\n          '需要一些安慰'\r\n        ],\r\n        angry: [\r\n          '我今天很生气',\r\n          '有些事情让我很不爽',\r\n          '感觉很愤怒',\r\n          '心情很烦躁'\r\n        ],\r\n        worried: [\r\n          '我有些担心',\r\n          '对未来感到不安',\r\n          '有些事情让我焦虑',\r\n          '心里有些不安'\r\n        ],\r\n        excited: [\r\n          '我超级兴奋！',\r\n          '有个好消息要分享',\r\n          '感觉充满活力',\r\n          '今天特别有动力'\r\n        ],\r\n        tired: [\r\n          '我感觉很累',\r\n          '今天很疲惫',\r\n          '需要休息一下',\r\n          '感觉没有精神'\r\n        ],\r\n        confused: [\r\n          '我有些困惑',\r\n          '不知道该怎么办',\r\n          '感觉很迷茫',\r\n          '需要一些建议'\r\n        ],\r\n        neutral: [\r\n          '今天过得还好',\r\n          '心情比较平静',\r\n          '想聊聊天',\r\n          '分享一下今天的事'\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    currentSuggestions() {\r\n      return this.emotionSuggestions[this.selectedEmotion] || this.emotionSuggestions.neutral;\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadUserInfo();\r\n    this.addWelcomeMessage();\r\n  },\r\n  methods: {\r\n    loadUserInfo() {\r\n      const userInfo = uni.getStorageSync('userInfo');\r\n      if (userInfo && userInfo.id) {\r\n        this.userId = userInfo.id;\r\n      }\r\n    },\r\n\r\n    addWelcomeMessage() {\r\n      this.messages.push({\r\n        type: 'ai',\r\n        content: '你好！我是你的情感陪伴助手💝\\n\\n无论你现在的心情如何，我都会用心倾听，给你温暖的陪伴和支持。请选择你当前的心情，或者直接告诉我你的感受吧～',\r\n        time: this.getCurrentTime()\r\n      });\r\n      this.scrollToBottom();\r\n    },\r\n\r\n    selectEmotion(emotionKey) {\r\n      this.selectedEmotion = emotionKey;\r\n      const emotion = this.emotions.find(e => e.key === emotionKey);\r\n      if (emotion && this.messages.length > 1) {\r\n        this.messages.push({\r\n          type: 'ai',\r\n          content: `我看到你现在的心情是${emotion.icon}${emotion.name}，想和我聊聊吗？我会认真倾听你的感受。`,\r\n          time: this.getCurrentTime()\r\n        });\r\n        this.scrollToBottom();\r\n      }\r\n    },\r\n\r\n    useSuggestion(suggestion) {\r\n      this.inputText = suggestion;\r\n      this.sendMessage();\r\n    },\r\n\r\n    async sendMessage() {\r\n      if (!this.inputText.trim() || this.isLoading) return;\r\n\r\n      const userMessage = {\r\n        type: 'user',\r\n        content: this.inputText.trim(),\r\n        time: this.getCurrentTime()\r\n      };\r\n\r\n      this.messages.push(userMessage);\r\n      const message = this.inputText.trim();\r\n      this.inputText = '';\r\n      this.isLoading = true;\r\n      this.isAiTyping = true;\r\n      this.scrollToBottom();\r\n\r\n      try {\r\n        // 调用AI情感陪伴API\r\n        const response = await this.callEmotionAPI(message);\r\n        \r\n        this.isAiTyping = false;\r\n        \r\n        if (response && response.success) {\r\n          this.messages.push({\r\n            type: 'ai',\r\n            content: response.response || '我理解你的感受，请继续和我分享吧。',\r\n            time: this.getCurrentTime()\r\n          });\r\n        } else {\r\n          this.messages.push({\r\n            type: 'ai',\r\n            content: '我现在有些忙，但我一直在这里陪伴你。请继续告诉我你的感受。',\r\n            time: this.getCurrentTime()\r\n          });\r\n        }\r\n      } catch (error) {\r\n        this.isAiTyping = false;\r\n        this.messages.push({\r\n          type: 'ai',\r\n          content: '抱歉，我现在无法很好地回应你，但请记住，你的感受很重要，我会一直在这里支持你。',\r\n          time: this.getCurrentTime()\r\n        });\r\n        console.error('API调用失败:', error);\r\n      }\r\n\r\n      this.isLoading = false;\r\n      this.scrollToBottom();\r\n    },\r\n\r\n    async callEmotionAPI(message) {\r\n      const apiUrl = 'http://localhost:8082/api/ai/miniprogram/emotion/companion';\r\n      \r\n      const response = await uni.request({\r\n        url: apiUrl,\r\n        method: 'POST',\r\n        header: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n          userId: this.userId,\r\n          message: message\r\n        }\r\n      });\r\n\r\n      if (response.statusCode === 200 && response.data.code === 200) {\r\n        return response.data.data;\r\n      } else {\r\n        throw new Error('API调用失败');\r\n      }\r\n    },\r\n\r\n    getCurrentTime() {\r\n      const now = new Date();\r\n      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;\r\n    },\r\n\r\n    scrollToBottom() {\r\n      this.$nextTick(() => {\r\n        this.scrollTop = 999999;\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: linear-gradient(135deg, #ffeef8 0%, #f0f8ff 100%);\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10rpx);\r\n  border-bottom: 1rpx solid rgba(233, 236, 239, 0.5);\r\n}\r\n\r\n.header-icon {\r\n  font-size: 48rpx;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.header-info {\r\n  flex: 1;\r\n}\r\n\r\n.header-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  display: block;\r\n}\r\n\r\n.header-desc {\r\n  font-size: 24rpx;\r\n  color: #6c757d;\r\n  margin-top: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.emotion-selector {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10rpx);\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid rgba(233, 236, 239, 0.5);\r\n}\r\n\r\n.selector-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.emotion-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 20rpx;\r\n}\r\n\r\n.emotion-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx 10rpx;\r\n  border-radius: 16rpx;\r\n  background: rgba(255, 255, 255, 0.7);\r\n  border: 2rpx solid transparent;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.emotion-item.active {\r\n  background: rgba(0, 123, 255, 0.1);\r\n  border-color: #007bff;\r\n  transform: scale(1.05);\r\n}\r\n\r\n.emotion-item:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n.emotion-icon {\r\n  font-size: 36rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.emotion-name {\r\n  font-size: 22rpx;\r\n  color: #495057;\r\n  font-weight: 500;\r\n}\r\n\r\n.emotion-item.active .emotion-name {\r\n  color: #007bff;\r\n  font-weight: bold;\r\n}\r\n\r\n.chat-area {\r\n  flex: 1;\r\n  padding: 20rpx;\r\n}\r\n\r\n.message-item {\r\n  display: flex;\r\n  margin-bottom: 30rpx;\r\n  animation: fadeInUp 0.3s ease;\r\n}\r\n\r\n.message-item.user {\r\n  flex-direction: row-reverse;\r\n}\r\n\r\n.message-avatar {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #ff6b9d, #c44569);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 20rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);\r\n}\r\n\r\n.message-item.user .message-avatar {\r\n  background: linear-gradient(135deg, #007bff, #0056b3);\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);\r\n}\r\n\r\n.avatar-text {\r\n  font-size: 24rpx;\r\n  color: #fff;\r\n  font-weight: bold;\r\n}\r\n\r\n.message-content {\r\n  flex: 1;\r\n  max-width: 70%;\r\n}\r\n\r\n.message-bubble {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10rpx);\r\n  padding: 20rpx;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.message-item.user .message-bubble {\r\n  background: linear-gradient(135deg, #007bff, #0056b3);\r\n}\r\n\r\n.message-text {\r\n  font-size: 28rpx;\r\n  line-height: 1.5;\r\n  color: #333;\r\n  display: block;\r\n  white-space: pre-wrap;\r\n}\r\n\r\n.message-item.user .message-text {\r\n  color: #fff;\r\n}\r\n\r\n.message-time {\r\n  font-size: 20rpx;\r\n  color: #999;\r\n  margin-top: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.message-item.user .message-time {\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.typing-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.dot {\r\n  width: 12rpx;\r\n  height: 12rpx;\r\n  border-radius: 50%;\r\n  background: #ff6b9d;\r\n  animation: typing 1.4s infinite;\r\n}\r\n\r\n.dot:nth-child(2) { animation-delay: 0.2s; }\r\n.dot:nth-child(3) { animation-delay: 0.4s; }\r\n\r\n@keyframes typing {\r\n  0%, 60%, 100% { opacity: 0.3; }\r\n  30% { opacity: 1; }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.input-area {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10rpx);\r\n  padding: 20rpx;\r\n  border-top: 1rpx solid rgba(233, 236, 239, 0.5);\r\n}\r\n\r\n.input-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n}\r\n\r\n.input-field {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  padding: 0 20rpx;\r\n  border: 1rpx solid rgba(233, 236, 239, 0.5);\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  background: rgba(248, 249, 250, 0.8);\r\n}\r\n\r\n.send-btn {\r\n  width: 120rpx;\r\n  height: 80rpx;\r\n  background: linear-gradient(135deg, #ff6b9d, #c44569);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 40rpx;\r\n  font-size: 28rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);\r\n}\r\n\r\n.send-btn.disabled {\r\n  background: #ccc;\r\n  color: #999;\r\n  box-shadow: none;\r\n}\r\n\r\n.emotion-suggestions {\r\n  position: absolute;\r\n  bottom: 140rpx;\r\n  left: 20rpx;\r\n  right: 20rpx;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(10rpx);\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.suggestions-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.suggestions-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.suggestion-item {\r\n  padding: 20rpx;\r\n  background: rgba(248, 249, 250, 0.8);\r\n  border-radius: 12rpx;\r\n  border: 1rpx solid rgba(233, 236, 239, 0.5);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.suggestion-item:active {\r\n  background: rgba(233, 236, 239, 0.8);\r\n  transform: scale(0.98);\r\n}\r\n\r\n.suggestion-text {\r\n  font-size: 26rpx;\r\n  color: #495057;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/study/study-project/s-sai/pages/ai-emotion/ai-emotion.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAmGA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,UAAU;AAAA,QACR,EAAE,KAAK,SAAS,MAAM,MAAM,MAAM,KAAM;AAAA,QACxC,EAAE,KAAK,OAAO,MAAM,MAAM,MAAM,KAAM;AAAA,QACtC,EAAE,KAAK,SAAS,MAAM,MAAM,MAAM,KAAM;AAAA,QACxC,EAAE,KAAK,WAAW,MAAM,MAAM,MAAM,KAAM;AAAA,QAC1C,EAAE,KAAK,WAAW,MAAM,MAAM,MAAM,KAAM;AAAA,QAC1C,EAAE,KAAK,SAAS,MAAM,MAAM,MAAM,KAAM;AAAA,QACxC,EAAE,KAAK,YAAY,MAAM,MAAM,MAAM,KAAM;AAAA,QAC3C,EAAE,KAAK,WAAW,MAAM,MAAM,MAAM,KAAK;AAAA,MAC1C;AAAA,MACD,oBAAoB;AAAA,QAClB,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,QACD,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,qBAAqB;AACnB,aAAO,KAAK,mBAAmB,KAAK,eAAe,KAAK,KAAK,mBAAmB;AAAA,IAClF;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,aAAY;AACjB,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,YAAY,SAAS,IAAI;AAC3B,aAAK,SAAS,SAAS;AAAA,MACzB;AAAA,IACD;AAAA,IAED,oBAAoB;AAClB,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM,KAAK,eAAe;AAAA,MAC5B,CAAC;AACD,WAAK,eAAc;AAAA,IACpB;AAAA,IAED,cAAc,YAAY;AACxB,WAAK,kBAAkB;AACvB,YAAM,UAAU,KAAK,SAAS,KAAK,OAAK,EAAE,QAAQ,UAAU;AAC5D,UAAI,WAAW,KAAK,SAAS,SAAS,GAAG;AACvC,aAAK,SAAS,KAAK;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,aAAa,QAAQ,IAAI,GAAG,QAAQ,IAAI;AAAA,UACjD,MAAM,KAAK,eAAe;AAAA,QAC5B,CAAC;AACD,aAAK,eAAc;AAAA,MACrB;AAAA,IACD;AAAA,IAED,cAAc,YAAY;AACxB,WAAK,YAAY;AACjB,WAAK,YAAW;AAAA,IACjB;AAAA,IAED,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK,UAAU,KAAI,KAAM,KAAK;AAAW;AAE9C,YAAM,cAAc;AAAA,QAClB,MAAM;AAAA,QACN,SAAS,KAAK,UAAU,KAAM;AAAA,QAC9B,MAAM,KAAK,eAAe;AAAA;AAG5B,WAAK,SAAS,KAAK,WAAW;AAC9B,YAAM,UAAU,KAAK,UAAU,KAAI;AACnC,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,eAAc;AAEnB,UAAI;AAEF,cAAM,WAAW,MAAM,KAAK,eAAe,OAAO;AAElD,aAAK,aAAa;AAElB,YAAI,YAAY,SAAS,SAAS;AAChC,eAAK,SAAS,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,SAAS,SAAS,YAAY;AAAA,YAC9B,MAAM,KAAK,eAAe;AAAA,UAC5B,CAAC;AAAA,eACI;AACL,eAAK,SAAS,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,SAAS;AAAA,YACT,MAAM,KAAK,eAAe;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACd,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK;AAAA,UACjB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,KAAK,eAAe;AAAA,QAC5B,CAAC;AACDA,sBAAc,MAAA,MAAA,SAAA,0CAAA,YAAY,KAAK;AAAA,MACjC;AAEA,WAAK,YAAY;AACjB,WAAK,eAAc;AAAA,IACpB;AAAA,IAED,MAAM,eAAe,SAAS;AAC5B,YAAM,SAAS;AAEf,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QACjC,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,MAAM;AAAA,UACJ,QAAQ,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,KAAK;AAC7D,eAAO,SAAS,KAAK;AAAA,aAChB;AACL,cAAM,IAAI,MAAM,SAAS;AAAA,MAC3B;AAAA,IACD;AAAA,IAED,iBAAiB;AACf,YAAM,MAAM,oBAAI;AAChB,aAAO,GAAG,IAAI,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC,IAAI,IAAI,WAAY,EAAC,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,IACrG;AAAA,IAED,iBAAiB;AACf,WAAK,UAAU,MAAM;AACnB,aAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxSA,GAAG,WAAW,eAAe;"}