<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ebc15272-3b84-403f-aea8-95855ddadaa8" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-gateway/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-gateway/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ArcFace64.dat" beforeDir="false" afterPath="$PROJECT_DIR$/ArcFace64.dat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30ZkANiQTp332gJGwSafbdOjQm7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.AppServiceApplication.executor": "Run",
    "Spring Boot.ArcsoftFaceServiceApplication.executor": "Run",
    "Spring Boot.GatewayApplication.executor": "Run",
    "Spring Boot.PcServiceApplication.executor": "Run",
    "Spring Boot.UserLoginApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.UserLoginApplication">
    <configuration name="AppServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="AiApp-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.AppServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ArcsoftFaceServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-face" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.ArcsoftFaceServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ImApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-im" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.im.ImApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PcServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="AiPC-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.PcServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserLoginApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-userlogin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.UserLoginApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ebc15272-3b84-403f-aea8-95855ddadaa8" name="Changes" comment="" />
      <created>1753842339331</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753842339331</updated>
      <workItem from="1753842340721" duration="7627000" />
      <workItem from="1753876384337" duration="1547000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>