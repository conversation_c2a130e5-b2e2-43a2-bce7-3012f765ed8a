# 服务器配置
server:
  port: 8084

spring:
  application:
    name: ai-im-service

  # 禁用Nacos（测试阶段）
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

  # 数据库配置
  datasource:
    url: **********************************************************************************************************************************************************************************
    username: root
    password: Sunshuo0818
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false

  # Redis配置
  redis:
    host: **************
    port: 6379
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# Netty WebSocket服务器配置
netty:
  websocket:
    port: 9999
    boss-threads: 1
    worker-threads: 4
    max-frame-size: 65536

# 系统配置
system:
  # 消息限流配置
  rate-limit:
    message-per-minute: 30
    
  # 缓存配置
  cache:
    recent-message-size: 50
    recent-message-expire-days: 7
    
  # 在线状态配置
  online-status:
    expire-hours: 24
    
  # 定时任务配置
  schedule:
    online-stats-update-minutes: 5
    cleanup-expired-data-hours: 1

# JWT配置
jwt:
  secret: aiTutorSecretKey2024ForImService
  expiration: 86400000  # 24小时

# 日志配置
logging:
  level:
    cn.zhentao: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
