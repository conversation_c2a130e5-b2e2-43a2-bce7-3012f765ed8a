package com.zhentao.studyim.repository;

import com.zhentao.studyim.entity.Friendship;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 好友关系Repository接口
 */
@Repository
public interface FriendshipRepository extends JpaRepository<Friendship, Long> {

    /**
     * 查找用户的所有好友
     * @param userId 用户ID
     * @param status 好友关系状态
     * @return 好友关系列表
     */
    List<Friendship> findByUserIdAndStatus(Long userId, Friendship.FriendshipStatus status);

    /**
     * 查找两个用户之间的好友关系
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 好友关系
     */
    Optional<Friendship> findByUserIdAndFriendId(Long userId, Long friendId);

    /**
     * 检查两个用户是否是好友
     * @param userId 用户ID
     * @param friendId 好友ID
     * @param status 好友关系状态
     * @return 是否存在好友关系
     */
    boolean existsByUserIdAndFriendIdAndStatus(Long userId, Long friendId, Friendship.FriendshipStatus status);

    /**
     * 查找用户的好友ID列表
     * @param userId 用户ID
     * @param status 好友关系状态
     * @return 好友ID列表
     */
    @Query("SELECT f.friendId FROM Friendship f WHERE f.userId = :userId AND f.status = :status")
    List<Long> findFriendIdsByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Friendship.FriendshipStatus status);

    /**
     * 删除两个用户之间的好友关系（双向删除）
     * @param userId 用户ID
     * @param friendId 好友ID
     */
    void deleteByUserIdAndFriendId(Long userId, Long friendId);

    /**
     * 查找用户的好友数量
     * @param userId 用户ID
     * @param status 好友关系状态
     * @return 好友数量
     */
    long countByUserIdAndStatus(Long userId, Friendship.FriendshipStatus status);
}
