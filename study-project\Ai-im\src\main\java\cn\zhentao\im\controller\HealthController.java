package cn.zhentao.im.controller;

import cn.zhentao.im.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/im/health")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class HealthController {

    private final DataSource dataSource;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 系统健康检查
     * GET /api/im/health
     */
    @GetMapping
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();

        // 检查数据库连接
        try (Connection connection = dataSource.getConnection()) {
            status.put("database", "UP");
        } catch (Exception e) {
            status.put("database", "DOWN - " + e.getMessage());
        }

        // 检查Redis连接
        try {
            redisTemplate.opsForValue().set("health:check", "ok");
            String result = (String) redisTemplate.opsForValue().get("health:check");
            status.put("redis", "ok".equals(result) ? "UP" : "DOWN");
        } catch (Exception e) {
            status.put("redis", "DOWN - Redis服务未启动或连接失败");
        }

        status.put("application", "UP");
        status.put("service", "ai-im-service");
        status.put("timestamp", System.currentTimeMillis());

        return ApiResponse.success(status);
    }
}
