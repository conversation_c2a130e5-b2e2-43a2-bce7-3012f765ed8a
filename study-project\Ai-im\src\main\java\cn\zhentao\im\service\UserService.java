package cn.zhentao.im.service;

import cn.zhentao.im.dto.LoginRequest;
import cn.zhentao.im.dto.RegisterRequest;
import cn.zhentao.im.entity.User;
import cn.zhentao.im.repository.UserRepository;
import cn.zhentao.im.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 用户业务逻辑服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final JwtUtil jwtUtil;

    /**
     * 用户注册
     */
    @Transactional
    public Map<String, Object> register(RegisterRequest request) {
        log.info("用户注册: username={}", request.getUsername());

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(request.getPassword()); // 实际应用中需要加密
        user.setNickname(request.getNickname() != null ? request.getNickname() : request.getUsername());
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        User savedUser = userRepository.save(user);
        log.info("用户注册成功: userId={}, username={}", savedUser.getUserId(), savedUser.getUsername());

        // 生成JWT令牌
        String token = jwtUtil.generateToken(savedUser.getUserId(), savedUser.getUsername());

        Map<String, Object> result = new HashMap<>();
        result.put("userId", savedUser.getUserId());
        result.put("username", savedUser.getUsername());
        result.put("nickname", savedUser.getNickname());
        result.put("token", token);

        return result;
    }

    /**
     * 用户登录
     */
    public Map<String, Object> login(LoginRequest request) {
        log.info("用户登录: username={}", request.getUsername());

        // 查找用户
        Optional<User> userOpt = userRepository.findByUsername(request.getUsername());
        if (userOpt.isEmpty()) {
            throw new RuntimeException("用户不存在");
        }

        User user = userOpt.get();

        // 验证密码（实际应用中需要验证加密后的密码）
        if (!user.getPassword().equals(request.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 更新登录时间
        user.setLoginDate(LocalDateTime.now());
        user.setUserStatus(User.UserStatus.ONLINE);
        userRepository.save(user);

        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getUserId(), user.getUsername());

        Map<String, Object> result = new HashMap<>();
        result.put("userId", user.getUserId());
        result.put("username", user.getUsername());
        result.put("nickname", user.getNickname());
        result.put("token", token);

        log.info("用户登录成功: userId={}, username={}", user.getUserId(), user.getUsername());
        return result;
    }

    /**
     * 根据ID查找用户
     */
    public Optional<User> findById(Long userId) {
        return userRepository.findById(userId);
    }

    /**
     * 根据用户名查找用户
     */
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }
}
