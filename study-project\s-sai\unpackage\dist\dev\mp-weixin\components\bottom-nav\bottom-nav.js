"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "BottomNav",
  props: {
    activeTab: {
      type: String,
      default: "home"
    }
  },
  methods: {
    switchTab(tab) {
      this.$emit("tab-change", tab);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.activeTab === "home" ? 1 : "",
    b: common_vendor.o(($event) => $options.switchTab("home")),
    c: $props.activeTab === "learn" ? 1 : "",
    d: common_vendor.o(($event) => $options.switchTab("learn")),
    e: $props.activeTab === "course" ? 1 : "",
    f: common_vendor.o(($event) => $options.switchTab("course")),
    g: $props.activeTab === "question" ? 1 : "",
    h: common_vendor.o(($event) => $options.switchTab("question")),
    i: $props.activeTab === "premium" ? 1 : "",
    j: common_vendor.o(($event) => $options.switchTab("premium")),
    k: $props.activeTab === "ai-home" ? 1 : "",
    l: common_vendor.o(($event) => $options.switchTab("ai-home"))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-55d8123a"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/bottom-nav/bottom-nav.js.map
