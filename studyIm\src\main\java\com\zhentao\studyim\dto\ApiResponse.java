package com.zhentao.studyim.dto;

import lombok.Data;

/**
 * 统一API响应格式
 * 所有接口都使用这个格式返回数据
 */
@Data
public class ApiResponse<T> {
    private int code;           // 响应码：200成功，其他失败
    private String message;     // 响应消息
    private T data;            // 响应数据

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(200);
        response.setMessage("success");
        response.setData(data);
        return response;
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(500);
        response.setMessage(message);
        return response;
    }

    /**
     * 自定义响应码的失败响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
}