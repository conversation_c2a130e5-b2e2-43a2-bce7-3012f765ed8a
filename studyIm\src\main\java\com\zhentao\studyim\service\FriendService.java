package com.zhentao.studyim.service;

import com.zhentao.studyim.entity.FriendRequest;
import com.zhentao.studyim.entity.Friendship;
import com.zhentao.studyim.entity.User;
import com.zhentao.studyim.repository.FriendRequestRepository;
import com.zhentao.studyim.repository.FriendshipRepository;
import com.zhentao.studyim.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 好友服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FriendService {

    private final FriendRequestRepository friendRequestRepository;
    private final FriendshipRepository friendshipRepository;
    private final UserRepository userRepository;

    /**
     * 搜索用户（通过用户名或手机号）
     * @param keyword 搜索关键词
     * @param currentUserId 当前用户ID
     * @return 用户列表
     */
    public List<User> searchUsers(String keyword, Long currentUserId) {
        log.info("搜索用户: keyword={}, currentUserId={}", keyword, currentUserId);
        
        // 搜索用户名或手机号匹配的用户，排除当前用户
        List<User> users = userRepository.findByUsernameContainingOrPhonenumberContaining(keyword, keyword);
        
        // 过滤掉当前用户
        return users.stream()
                .filter(user -> !user.getUserId().equals(currentUserId))
                .toList();
    }

    /**
     * 发送好友申请
     * @param fromUserId 申请人用户ID
     * @param toUserId 被申请人用户ID
     * @param message 申请消息
     * @return 申请结果
     */
    @Transactional
    public String sendFriendRequest(Long fromUserId, Long toUserId, String message) {
        log.info("发送好友申请: fromUserId={}, toUserId={}, message={}", fromUserId, toUserId, message);
        
        // 检查用户是否存在
        if (!userRepository.existsById(fromUserId) || !userRepository.existsById(toUserId)) {
            return "用户不存在";
        }
        
        // 不能向自己发送好友申请
        if (fromUserId.equals(toUserId)) {
            return "不能向自己发送好友申请";
        }
        
        // 检查是否已经是好友
        if (friendshipRepository.existsByUserIdAndFriendIdAndStatus(fromUserId, toUserId, Friendship.FriendshipStatus.ACTIVE)) {
            return "已经是好友了";
        }
        
        // 检查是否已经发送过申请
        if (friendRequestRepository.existsByFromUserIdAndToUserIdAndStatus(fromUserId, toUserId, FriendRequest.RequestStatus.PENDING)) {
            return "已经发送过好友申请，请等待对方回复";
        }
        
        // 创建好友申请
        FriendRequest friendRequest = new FriendRequest();
        friendRequest.setFromUserId(fromUserId);
        friendRequest.setToUserId(toUserId);
        friendRequest.setMessage(message);
        friendRequest.setStatus(FriendRequest.RequestStatus.PENDING);
        
        friendRequestRepository.save(friendRequest);
        
        log.info("好友申请发送成功: {}", friendRequest);
        return "好友申请发送成功";
    }

    /**
     * 获取收到的好友申请列表
     * @param userId 用户ID
     * @return 好友申请列表
     */
    public List<FriendRequest> getReceivedFriendRequests(Long userId) {
        log.info("获取收到的好友申请: userId={}", userId);
        return friendRequestRepository.findByToUserIdAndStatus(userId, FriendRequest.RequestStatus.PENDING);
    }

    /**
     * 获取发出的好友申请列表
     * @param userId 用户ID
     * @return 好友申请列表
     */
    public List<FriendRequest> getSentFriendRequests(Long userId) {
        log.info("获取发出的好友申请: userId={}", userId);
        return friendRequestRepository.findByFromUserIdAndStatus(userId, FriendRequest.RequestStatus.PENDING);
    }

    /**
     * 同意好友申请
     * @param requestId 申请ID
     * @param userId 当前用户ID
     * @return 处理结果
     */
    @Transactional
    public String acceptFriendRequest(Long requestId, Long userId) {
        log.info("同意好友申请: requestId={}, userId={}", requestId, userId);
        
        Optional<FriendRequest> requestOpt = friendRequestRepository.findById(requestId);
        if (requestOpt.isEmpty()) {
            return "好友申请不存在";
        }
        
        FriendRequest request = requestOpt.get();
        
        // 检查申请是否是发给当前用户的
        if (!request.getToUserId().equals(userId)) {
            return "无权处理此申请";
        }
        
        // 检查申请状态
        if (request.getStatus() != FriendRequest.RequestStatus.PENDING) {
            return "申请已经被处理过了";
        }
        
        // 更新申请状态
        request.setStatus(FriendRequest.RequestStatus.ACCEPTED);
        friendRequestRepository.save(request);
        
        // 创建双向好友关系
        createFriendship(request.getFromUserId(), request.getToUserId());
        createFriendship(request.getToUserId(), request.getFromUserId());
        
        log.info("好友申请同意成功: {}", request);
        return "已同意好友申请";
    }

    /**
     * 拒绝好友申请
     * @param requestId 申请ID
     * @param userId 当前用户ID
     * @return 处理结果
     */
    @Transactional
    public String rejectFriendRequest(Long requestId, Long userId) {
        log.info("拒绝好友申请: requestId={}, userId={}", requestId, userId);
        
        Optional<FriendRequest> requestOpt = friendRequestRepository.findById(requestId);
        if (requestOpt.isEmpty()) {
            return "好友申请不存在";
        }
        
        FriendRequest request = requestOpt.get();
        
        // 检查申请是否是发给当前用户的
        if (!request.getToUserId().equals(userId)) {
            return "无权处理此申请";
        }
        
        // 检查申请状态
        if (request.getStatus() != FriendRequest.RequestStatus.PENDING) {
            return "申请已经被处理过了";
        }
        
        // 更新申请状态
        request.setStatus(FriendRequest.RequestStatus.REJECTED);
        friendRequestRepository.save(request);
        
        log.info("好友申请拒绝成功: {}", request);
        return "已拒绝好友申请";
    }

    /**
     * 获取好友列表
     * @param userId 用户ID
     * @return 好友列表
     */
    public List<User> getFriendList(Long userId) {
        log.info("获取好友列表: userId={}", userId);
        
        // 获取好友ID列表
        List<Long> friendIds = friendshipRepository.findFriendIdsByUserIdAndStatus(userId, Friendship.FriendshipStatus.ACTIVE);
        
        // 根据ID列表获取用户信息
        return userRepository.findAllById(friendIds);
    }

    /**
     * 删除好友
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 删除结果
     */
    @Transactional
    public String deleteFriend(Long userId, Long friendId) {
        log.info("删除好友: userId={}, friendId={}", userId, friendId);
        
        // 删除双向好友关系
        friendshipRepository.deleteByUserIdAndFriendId(userId, friendId);
        friendshipRepository.deleteByUserIdAndFriendId(friendId, userId);
        
        log.info("好友删除成功");
        return "好友删除成功";
    }

    /**
     * 检查是否是好友
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 是否是好友
     */
    public boolean isFriend(Long userId, Long friendId) {
        return friendshipRepository.existsByUserIdAndFriendIdAndStatus(userId, friendId, Friendship.FriendshipStatus.ACTIVE);
    }

    /**
     * 创建好友关系
     * @param userId 用户ID
     * @param friendId 好友ID
     */
    private void createFriendship(Long userId, Long friendId) {
        Friendship friendship = new Friendship();
        friendship.setUserId(userId);
        friendship.setFriendId(friendId);
        friendship.setStatus(Friendship.FriendshipStatus.ACTIVE);
        friendshipRepository.save(friendship);
    }

    /**
     * 获取待处理的好友申请数量
     * @param userId 用户ID
     * @return 申请数量
     */
    public long getPendingRequestCount(Long userId) {
        return friendRequestRepository.countByToUserIdAndStatus(userId, FriendRequest.RequestStatus.PENDING);
    }
}
