package com.zhentao.studyim.controller;

import com.zhentao.studyim.dto.ApiResponse;
import com.zhentao.studyim.service.RedisService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计信息控制器
 * 提供系统统计数据
 */
@RestController
@RequestMapping("/api/stats")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class StatsController {

    private final RedisService redisService;

    /**
     * 获取系统统计信息
     * GET /api/stats
     */
    @GetMapping
    public ApiResponse<Map<String, Object>> getStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 在线用户统计
        int onlineUserCount = redisService.getOnlineUserCount();
        List<Long> onlineUserIds = redisService.getOnlineUserIds();
        
        // 消息统计
        long todayMessageCount = redisService.getTodayMessageCount();
        
        stats.put("onlineUserCount", onlineUserCount);
        stats.put("onlineUserIds", onlineUserIds);
        stats.put("todayMessageCount", todayMessageCount);
        stats.put("timestamp", System.currentTimeMillis());
        
        return ApiResponse.success(stats);
    }

    /**
     * 获取在线用户列表
     * GET /api/stats/online-users
     */
    @GetMapping("/online-users")
    public ApiResponse<List<Long>> getOnlineUsers() {
        List<Long> onlineUsers = redisService.getOnlineUserIds();
        return ApiResponse.success(onlineUsers);
    }

    /**
     * 获取用户会话信息
     * GET /api/stats/user-session/{userId}
     */
    @GetMapping("/user-session/{userId}")
    public ApiResponse<Map<Object, Object>> getUserSession(@PathVariable Long userId) {
        Map<Object, Object> session = redisService.getUserSession(userId);
        return ApiResponse.success(session);
    }

    /**
     * 获取最近的缓存消息
     * GET /api/stats/recent-messages
     */
    @GetMapping("/recent-messages")
    public ApiResponse<List<Map<String, Object>>> getRecentMessages(
            @RequestParam Long userId1,
            @RequestParam Long userId2,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<Map<String, Object>> messages = redisService.getRecentMessages(userId1, userId2, limit);
        return ApiResponse.success(messages);
    }

    /**
     * 清理过期数据
     * POST /api/stats/clean
     */
    @PostMapping("/clean")
    public ApiResponse<String> cleanExpiredData() {
        redisService.cleanExpiredData();
        return ApiResponse.success("过期数据清理完成");
    }

    /**
     * 检查用户是否在线
     * GET /api/stats/user-online/{userId}
     */
    @GetMapping("/user-online/{userId}")
    public ApiResponse<Map<String, Object>> checkUserOnline(@PathVariable Long userId) {
        boolean isOnline = redisService.isUserOnline(userId);
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        result.put("isOnline", isOnline);
        result.put("timestamp", System.currentTimeMillis());
        
        return ApiResponse.success(result);
    }
}
